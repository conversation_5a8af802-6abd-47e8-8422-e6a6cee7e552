<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Addresses</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
        }
        .address-card {
            transition: all 0.2s ease;
        }
        .address-card:hover {
            background-color: #f1f3f4;
        }
        .add-address-btn {
            transition: all 0.2s ease;
        }
        .add-address-btn:hover {
            background-color: #f1f3f4;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen bg-white">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-white border-b border-gray-200">
            <div class="flex items-center justify-center px-4 py-4 relative">
                <button onclick="goBack()" class="absolute left-4 p-2 rounded-full hover:bg-gray-100 transition-all">
                    <i class="fas fa-chevron-left text-xl text-black"></i>
                </button>
                <h1 class="text-xl font-bold text-black">Your addresses</h1>
            </div>
        </div>

        <!-- Content -->
        <div class="px-4 py-6">
            <!-- Add Address Button -->
            <div class="add-address-btn flex items-center justify-between p-4 mb-6 cursor-pointer border-b border-gray-200" onclick="showAddAddressPage()">
                <div class="flex items-center space-x-3">
                    <div class="w-6 h-6 flex items-center justify-center">
                        <i class="fas fa-plus text-black text-lg"></i>
                    </div>
                    <span class="text-black font-medium">Add address</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <!-- Existing Addresses -->
            <div id="addressesList">
                <!-- Sample Address -->
                <div class="address-card bg-white p-4 mb-4 border-b border-gray-200">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-black">daryl Belhida</h3>
                                <button class="text-red-500 font-medium text-sm">Edit</button>
                            </div>
                            <p class="text-gray-600 text-sm mb-1">(+63)93******03</p>
                            <p class="text-gray-600 text-sm mb-1">San Jose talibon bohol, San Jose Barangay</p>
                            <p class="text-gray-600 text-sm mb-1">Road, Talibon,near cemetery</p>
                            <p class="text-gray-600 text-sm mb-2">San Jose, Talibon, Bohol, Philippines</p>
                            <span class="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">Default</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            // Close this page and return to previous view
            if (window.closeAddressesPage) {
                window.closeAddressesPage();
            } else {
                window.history.back();
            }
        }

        function showAddAddressPage() {
            // Create and show the add address page
            const addAddressPage = document.createElement('div');
            addAddressPage.id = 'addAddressPageOverlay';
            addAddressPage.className = 'fixed inset-0 z-[400] bg-white';
            
            addAddressPage.innerHTML = `
                <!-- Header -->
                <div class="sticky top-0 z-50 bg-white border-b border-gray-200">
                    <div class="flex items-center justify-center px-4 py-4 relative">
                        <button onclick="closeAddAddressPage()" class="absolute left-4 p-2 rounded-full hover:bg-gray-100 transition-all">
                            <i class="fas fa-chevron-left text-xl text-black"></i>
                        </button>
                        <h1 class="text-xl font-bold text-black">Add new address</h1>
                    </div>
                </div>

                <!-- Content -->
                <div class="px-4 py-6 bg-gray-50 min-h-screen">
                    <!-- Contact Information -->
                    <div class="mb-6">
                        <h2 class="text-gray-500 text-sm font-medium mb-4">Contact information</h2>
                        
                        <!-- Name Input -->
                        <div class="mb-4">
                            <input type="text" id="contactName" placeholder="Enter full name" 
                                   class="w-full p-3 border-b border-gray-300 bg-transparent focus:border-black focus:outline-none text-black">
                        </div>
                        
                        <!-- Phone Input -->
                        <div class="flex items-center border-b border-gray-300">
                            <select class="p-3 bg-transparent focus:outline-none text-black">
                                <option>PH +63</option>
                            </select>
                            <input type="tel" id="phoneNumber" placeholder="Enter a valid phone number" 
                                   class="flex-1 p-3 bg-transparent focus:outline-none text-black">
                        </div>
                    </div>

                    <!-- Address Information -->
                    <div class="mb-6">
                        <h2 class="text-gray-500 text-sm font-medium mb-4">Address information</h2>
                        
                        <!-- Country Dropdown -->
                        <div class="mb-4">
                            <select class="w-full p-3 border-b border-gray-300 bg-transparent focus:border-black focus:outline-none text-black">
                                <option>Philippines</option>
                            </select>
                        </div>
                        
                        <!-- Region Selection -->
                        <div class="mb-4">
                            <button id="regionSelector" onclick="showProvinceSelector()" 
                                    class="w-full p-3 border-b border-gray-300 bg-transparent text-left text-gray-400 flex items-center justify-between">
                                <span id="regionText">Select region</span>
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </button>
                        </div>
                        
                        <!-- Warning Message -->
                        <div class="flex items-center space-x-2 mb-4">
                            <i class="fas fa-exclamation-triangle text-red-500 text-sm"></i>
                            <span class="text-red-500 text-sm">Make sure region matches address</span>
                        </div>
                        
                        <!-- Address Details -->
                        <div class="mb-4">
                            <textarea id="addressDetails" placeholder="Enter address information to get suggestions" 
                                      class="w-full p-3 border-b border-gray-300 bg-transparent focus:border-black focus:outline-none text-black resize-none h-16"></textarea>
                        </div>
                        
                        <!-- Other Details -->
                        <div class="mb-6">
                            <textarea id="otherDetails" placeholder="Enter other details (optional)" 
                                      class="w-full p-3 border-b border-gray-300 bg-transparent focus:border-black focus:outline-none text-black resize-none h-16"></textarea>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="mb-8">
                        <h2 class="text-gray-500 text-sm font-medium mb-4">Settings</h2>
                        
                        <!-- Set as Default Toggle -->
                        <div class="flex items-center justify-between py-3">
                            <span class="text-black font-medium">Set as default</span>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer" id="defaultToggle">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"></div>
                            </label>
                        </div>
                    </div>

                    <!-- Privacy Policy -->
                    <div class="mb-6">
                        <p class="text-gray-500 text-sm text-center">
                            By clicking Save, you acknowledge that you have read the 
                            <span class="text-black font-medium">Privacy Policy</span>.
                        </p>
                    </div>

                    <!-- Save Button -->
                    <button onclick="saveAddress()" 
                            class="w-full bg-pink-400 text-white py-4 rounded-lg font-medium text-lg hover:bg-pink-500 transition-all">
                        Save
                    </button>
                </div>
            `;
            
            document.body.appendChild(addAddressPage);
        }

        function closeAddAddressPage() {
            const addAddressPage = document.getElementById('addAddressPageOverlay');
            if (addAddressPage) {
                addAddressPage.remove();
            }
        }

        function saveAddress() {
            // Here you would implement the save functionality
            alert('Address saved successfully!');
            closeAddAddressPage();
        }

        // Address selection state
        let selectedProvince = '';
        let selectedMunicipality = '';
        let selectedBarangay = '';

        // Sample data for Philippines locations
        const philippinesData = {
            'Abra': {
                'Bangued': ['Agtangao', 'Angad', 'Bangbangcag', 'Cosili East', 'Cosili West'],
                'Boliney': ['Amti', 'Dao-angan', 'Kilong-olao', 'Poblacion'],
                'Bucay': ['Bangbangcag', 'Bangbangcag East', 'Bangbangcag West']
            },
            'Agusan del Norte': {
                'Buenavista': ['Abilan', 'Agong-ong', 'Alubijid', 'Guinabsan', 'Lower Olave'],
                'Butuan City': ['Agusan Pequeño', 'Ambago', 'Amparo', 'Anticala', 'Aupagan'],
                'Cabadbaran City': ['Bay-ang', 'Bayabas', 'Cabinet', 'Calibunan', 'Calamba']
            },
            'Agusan del Sur': {
                'Bayugan': ['Anahaw', 'Bayugan 3', 'Claro M. Recto', 'Ebro', 'Grace Village'],
                'Bunawan': ['Bunawan Brook', 'Consuelo', 'Libertad', 'Nueva Era', 'San Andres']
            },
            'Aklan': {
                'Kalibo': ['Andagao', 'Bachaw Norte', 'Bachaw Sur', 'Bugtong Bato', 'Buswang'],
                'Boracay': ['Balabag', 'Manoc-Manoc', 'Yapak']
            },
            'Albay': {
                'Legazpi City': ['Arimbay', 'Bagumbayan', 'Banquerohan', 'Bitano', 'Bonot'],
                'Tabaco City': ['Bagasbas', 'Bombon', 'Buhi', 'Cagbacong', 'Canaway']
            },
            'Bohol': {
                'Talibon': ['San Jose', 'Poblacion', 'Bagacay', 'Barangay 1', 'Barangay 2'],
                'Tagbilaran City': ['Bool', 'Booy', 'Cabawan', 'Cogon', 'Dampas'],
                'Carmen': ['Alegria', 'Buenos Aires', 'Katipunan', 'La Paz', 'Poblacion']
            }
        };

        function showProvinceSelector() {
            const modal = document.createElement('div');
            modal.id = 'provinceModal';
            modal.className = 'fixed inset-0 z-[500] bg-black bg-opacity-50 flex items-end';

            const provinces = Object.keys(philippinesData);
            const alphabetIndex = {};

            provinces.forEach(province => {
                const firstLetter = province.charAt(0).toUpperCase();
                if (!alphabetIndex[firstLetter]) {
                    alphabetIndex[firstLetter] = [];
                }
                alphabetIndex[firstLetter].push(province);
            });

            modal.innerHTML = `
                <div class="bg-white w-full max-h-[80vh] rounded-t-3xl overflow-hidden">
                    <!-- Header -->
                    <div class="flex items-center justify-between p-4 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-black">Select Province</h2>
                        <button onclick="closeModal('provinceModal')" class="p-2 rounded-full hover:bg-gray-100">
                            <i class="fas fa-times text-xl text-black"></i>
                        </button>
                    </div>

                    <!-- Content -->
                    <div class="flex">
                        <!-- Province List -->
                        <div class="flex-1 overflow-y-auto max-h-[60vh]">
                            <div class="p-4">
                                <div class="border-b border-black pb-2 mb-4">
                                    <span class="font-semibold text-black">Province</span>
                                </div>
                                ${Object.keys(alphabetIndex).sort().map(letter => `
                                    <div class="mb-4">
                                        <div class="text-lg font-bold text-black mb-2">${letter}</div>
                                        ${alphabetIndex[letter].map(province => `
                                            <div class="py-3 cursor-pointer hover:bg-gray-100 text-black" onclick="selectProvince('${province}')">
                                                ${province}
                                            </div>
                                        `).join('')}
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <!-- Alphabet Index -->
                        <div class="w-8 bg-gray-50 flex flex-col items-center justify-center py-4">
                            ${Object.keys(alphabetIndex).sort().map(letter => `
                                <div class="text-xs text-red-500 py-1">${letter}</div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function selectProvince(province) {
            selectedProvince = province;
            closeModal('provinceModal');

            // Update the region text to show selected province
            document.getElementById('regionText').textContent = province;
            document.getElementById('regionText').classList.remove('text-gray-400');
            document.getElementById('regionText').classList.add('text-black');

            // Automatically show municipality selector
            setTimeout(() => showMunicipalitySelector(), 300);
        }

        function showMunicipalitySelector() {
            if (!selectedProvince) return;

            const modal = document.createElement('div');
            modal.id = 'municipalityModal';
            modal.className = 'fixed inset-0 z-[500] bg-black bg-opacity-50 flex items-end';

            const municipalities = Object.keys(philippinesData[selectedProvince] || {});

            modal.innerHTML = `
                <div class="bg-white w-full max-h-[80vh] rounded-t-3xl overflow-hidden">
                    <!-- Header -->
                    <div class="flex items-center justify-between p-4 border-b border-gray-200">
                        <div class="flex items-center space-x-4">
                            <button onclick="goBackToProvince()" class="p-2 rounded-full hover:bg-gray-100">
                                <i class="fas fa-chevron-left text-xl text-black"></i>
                            </button>
                            <h2 class="text-xl font-bold text-black">Select Municipality</h2>
                        </div>
                        <button onclick="closeModal('municipalityModal')" class="p-2 rounded-full hover:bg-gray-100">
                            <i class="fas fa-times text-xl text-black"></i>
                        </button>
                    </div>

                    <!-- Breadcrumb -->
                    <div class="px-4 py-2 bg-gray-50 border-b border-gray-200">
                        <div class="flex items-center space-x-2 text-sm">
                            <span class="text-gray-500">${selectedProvince}</span>
                            <span class="text-black font-semibold border-b-2 border-black pb-1">Municipality</span>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="overflow-y-auto max-h-[60vh] p-4">
                        ${municipalities.length > 0 ? municipalities.map(municipality => `
                            <div class="py-3 cursor-pointer hover:bg-gray-100 text-black border-b border-gray-100" onclick="selectMunicipality('${municipality}')">
                                ${municipality}
                            </div>
                        `).join('') : '<div class="text-gray-500 text-center py-8">No municipalities available</div>'}
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function selectMunicipality(municipality) {
            selectedMunicipality = municipality;
            closeModal('municipalityModal');

            // Update the region text to show province and municipality
            document.getElementById('regionText').textContent = `${selectedProvince}, ${municipality}`;

            // Automatically show barangay selector
            setTimeout(() => showBarangaySelector(), 300);
        }

        function showBarangaySelector() {
            if (!selectedProvince || !selectedMunicipality) return;

            const modal = document.createElement('div');
            modal.id = 'barangayModal';
            modal.className = 'fixed inset-0 z-[500] bg-black bg-opacity-50 flex items-end';

            const barangays = philippinesData[selectedProvince]?.[selectedMunicipality] || [];

            modal.innerHTML = `
                <div class="bg-white w-full max-h-[80vh] rounded-t-3xl overflow-hidden">
                    <!-- Header -->
                    <div class="flex items-center justify-between p-4 border-b border-gray-200">
                        <div class="flex items-center space-x-4">
                            <button onclick="goBackToMunicipality()" class="p-2 rounded-full hover:bg-gray-100">
                                <i class="fas fa-chevron-left text-xl text-black"></i>
                            </button>
                            <h2 class="text-xl font-bold text-black">Select Barangay</h2>
                        </div>
                        <button onclick="closeModal('barangayModal')" class="p-2 rounded-full hover:bg-gray-100">
                            <i class="fas fa-times text-xl text-black"></i>
                        </button>
                    </div>

                    <!-- Breadcrumb -->
                    <div class="px-4 py-2 bg-gray-50 border-b border-gray-200">
                        <div class="flex items-center space-x-2 text-sm">
                            <span class="text-gray-500">${selectedProvince}</span>
                            <span class="text-gray-500">${selectedMunicipality}</span>
                            <span class="text-black font-semibold border-b-2 border-black pb-1">Barangay</span>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="overflow-y-auto max-h-[60vh] p-4">
                        ${barangays.length > 0 ? barangays.map(barangay => `
                            <div class="py-3 cursor-pointer hover:bg-gray-100 text-black border-b border-gray-100" onclick="selectBarangay('${barangay}')">
                                ${barangay}
                            </div>
                        `).join('') : '<div class="text-gray-500 text-center py-8">No barangays available</div>'}
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function selectBarangay(barangay) {
            selectedBarangay = barangay;
            closeModal('barangayModal');

            // Update the region text to show full address
            document.getElementById('regionText').textContent = `${selectedProvince}, ${selectedMunicipality}, ${barangay}`;
        }

        function goBackToProvince() {
            closeModal('municipalityModal');
            setTimeout(() => showProvinceSelector(), 300);
        }

        function goBackToMunicipality() {
            closeModal('barangayModal');
            setTimeout(() => showMunicipalitySelector(), 300);
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.remove();
            }
        }

        // Make functions globally available
        window.closeAddAddressPage = closeAddAddressPage;
        window.showAddAddressPage = showAddAddressPage;
        window.saveAddress = saveAddress;
        window.showProvinceSelector = showProvinceSelector;
        window.selectProvince = selectProvince;
        window.selectMunicipality = selectMunicipality;
        window.selectBarangay = selectBarangay;
        window.goBackToProvince = goBackToProvince;
        window.goBackToMunicipality = goBackToMunicipality;
        window.closeModal = closeModal;
    </script>
</body>
</html>
