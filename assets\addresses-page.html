<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Addresses</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
        }
        .address-card {
            transition: all 0.2s ease;
        }
        .address-card:hover {
            background-color: #f1f3f4;
        }
        .add-address-btn {
            transition: all 0.2s ease;
        }
        .add-address-btn:hover {
            background-color: #f1f3f4;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen bg-white">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-white border-b border-gray-200">
            <div class="flex items-center justify-center px-4 py-4 relative">
                <button onclick="goBack()" class="absolute left-4 p-2 rounded-full hover:bg-gray-100 transition-all">
                    <i class="fas fa-chevron-left text-xl text-black"></i>
                </button>
                <h1 class="text-xl font-bold text-black">Your addresses</h1>
            </div>
        </div>

        <!-- Content -->
        <div class="px-4 py-6">
            <!-- Add Address Button -->
            <div class="add-address-btn flex items-center justify-between p-4 mb-6 cursor-pointer border-b border-gray-200" onclick="showAddAddressPage()">
                <div class="flex items-center space-x-3">
                    <div class="w-6 h-6 flex items-center justify-center">
                        <i class="fas fa-plus text-black text-lg"></i>
                    </div>
                    <span class="text-black font-medium">Add address</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <!-- Existing Addresses -->
            <div id="addressesList">
                <!-- Sample Address -->
                <div class="address-card bg-white p-4 mb-4 border-b border-gray-200">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-black">daryl Belhida</h3>
                                <button class="text-red-500 font-medium text-sm">Edit</button>
                            </div>
                            <p class="text-gray-600 text-sm mb-1">(+63)93******03</p>
                            <p class="text-gray-600 text-sm mb-1">San Jose talibon bohol, San Jose Barangay</p>
                            <p class="text-gray-600 text-sm mb-1">Road, Talibon,near cemetery</p>
                            <p class="text-gray-600 text-sm mb-2">San Jose, Talibon, Bohol, Philippines</p>
                            <span class="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">Default</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load external JavaScript -->
    <script src="assets/addresses-logic.js"></script>
</body>
</html>
