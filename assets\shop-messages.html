<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop Messages - Picasso</title>
    
    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .message-item {
            transition: all 0.2s ease;
        }
        
        .message-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }
        
        .shop-update-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        
        .message-avatar {
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .back-button {
            transition: transform 0.2s ease;
        }
        
        .back-button:hover {
            transform: translateX(-2px);
        }
        
        .section-divider {
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        .message-time {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .message-preview {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .shop-update-badge {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body class="text-white min-h-screen">
    <!-- Header -->
    <div class="sticky top-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div class="flex items-center px-4 py-4">
            <button onclick="goBack()" class="back-button mr-4 p-2 rounded-full hover:bg-white/10">
                <i class="fas fa-chevron-left text-xl"></i>
            </button>
            <h1 class="text-xl font-bold">Shop messages</h1>
        </div>
    </div>

    <!-- Content -->
    <div class="px-4 py-6">
        <!-- Shop Updates Section -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4 text-white/90">Shop updates</h2>
            
            <!-- Cancellation Request Update -->
            <div class="message-item flex items-start space-x-4 p-4 rounded-xl mb-4">
                <div class="shop-update-icon w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-shopping-bag text-white text-lg"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between mb-1">
                        <h3 class="font-semibold text-white truncate">Your cancellation request was appro...</h3>
                        <span class="message-time text-sm ml-2 flex-shrink-0">3/14</span>
                    </div>
                    <p class="message-preview text-sm leading-relaxed">
                        Your cancellation request for order 578212471084647611 was approved by TikTok.
                    </p>
                </div>
            </div>

            <!-- Order Placed Update -->
            <div class="message-item flex items-start space-x-4 p-4 rounded-xl mb-4">
                <div class="shop-update-icon w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-shopping-bag text-white text-lg"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between mb-1">
                        <h3 class="font-semibold text-white">Order placed</h3>
                        <span class="message-time text-sm ml-2 flex-shrink-0">3/14</span>
                    </div>
                    <p class="message-preview text-sm leading-relaxed mb-3">
                        Your order 578212471084647611 was submitted. Thanks for shopping with TikTok!
                    </p>
                    <!-- Order Image -->
                    <div class="w-16 h-16 rounded-lg overflow-hidden">
                        <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&fit=crop" 
                             alt="Order item" class="w-full h-full object-cover">
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Divider -->
        <hr class="section-divider border-t my-6">

        <!-- Messages Section -->
        <div>
            <h2 class="text-lg font-semibold mb-4 text-white/90">Messages</h2>
            
            <!-- UNLIRICE RICE WHOLESALING -->
            <div class="message-item flex items-center space-x-4 p-4 rounded-xl mb-4 cursor-pointer" onclick="openChat('unlirice')">
                <div class="message-avatar w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                    <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=48&h=48&fit=crop" 
                         alt="UNLIRICE" class="w-full h-full object-cover">
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white truncate">UNLIRICE RICE WHOLESALING</h3>
                        <span class="message-time text-sm flex-shrink-0">1d</span>
                    </div>
                    <p class="message-preview text-sm truncate">Hello, How can we help you?</p>
                </div>
            </div>

            <!-- kooruiphilippines -->
            <div class="message-item flex items-center space-x-4 p-4 rounded-xl mb-4 cursor-pointer" onclick="openChat('koorui')">
                <div class="message-avatar w-12 h-12 rounded-full overflow-hidden flex-shrink-0 bg-red-500 flex items-center justify-center">
                    <span class="text-white font-bold text-lg">K</span>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white truncate">kooruiphilippines</h3>
                        <span class="message-time text-sm flex-shrink-0">05-03</span>
                    </div>
                    <p class="message-preview text-sm truncate">Good day, we've noticed that you can...</p>
                </div>
            </div>

            <!-- DC STORE.PH -->
            <div class="message-item flex items-center space-x-4 p-4 rounded-xl mb-4 cursor-pointer" onclick="openChat('dcstore')">
                <div class="message-avatar w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                    <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=48&h=48&fit=crop" 
                         alt="DC STORE" class="w-full h-full object-cover">
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white truncate">DC STORE.PH</h3>
                        <span class="message-time text-sm flex-shrink-0">02-19</span>
                    </div>
                    <p class="message-preview text-sm truncate">Thank you for getting in touch. How ...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            // Navigate back to the main shop page
            window.history.back();
        }

        function openChat(chatId) {
            // Navigate to individual chat page (to be implemented)
            alert(`Opening chat with ${chatId}`);
        }

        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add touch feedback for mobile
            const messageItems = document.querySelectorAll('.message-item');
            messageItems.forEach(item => {
                item.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });
                
                item.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
