// Address Management Logic
document.addEventListener('DOMContentLoaded', function() {
    console.log('Addresses page loaded');
});

// Address selection state
let selectedProvince = '';
let selectedMunicipality = '';
let selectedBarangay = '';

// Sample data for Philippines locations
const philippinesData = {
    'Abra': {
        'Bangued': ['Agtangao', 'Angad', 'Bangbangcag', 'Cosili East', 'Cosili West'],
        'Boliney': ['Amti', 'Dao-angan', 'Kilong-olao', 'Poblacion'],
        'Bucay': ['Bangbangcag', 'Bangbangcag East', 'Bangbangcag West']
    },
    'Agusan del Norte': {
        'Buenavista': ['<PERSON><PERSON>an', 'Agong-ong', 'Alubijid', 'Guinabsan', 'Lower Olave'],
        'Butuan City': ['Agusan Pequeño', 'Ambago', 'Amparo', 'Anticala', 'Aupagan'],
        'Cabadbaran City': ['Bay-ang', 'Bayabas', 'Cabinet', 'Calibunan', 'Calam<PERSON>']
    },
    'Agusan del Sur': {
        'Bayugan': ['<PERSON><PERSON>', 'Bayugan 3', 'Claro M. Recto', 'Ebro', 'Grace Village'],
        'Bunawan': ['Bunawan Brook', 'Consuelo', 'Libertad', 'Nueva Era', 'San Andres']
    },
    'Aklan': {
        'Kalibo': ['Andagao', 'Bachaw Norte', 'Bachaw Sur', 'Bugtong Bato', 'Buswang'],
        'Boracay': ['Balabag', 'Manoc-Manoc', 'Yapak']
    },
    'Albay': {
        'Legazpi City': ['Arimbay', 'Bagumbayan', 'Banquerohan', 'Bitano', 'Bonot'],
        'Tabaco City': ['Bagasbas', 'Bombon', 'Buhi', 'Cagbacong', 'Canaway']
    },
    'Bohol': {
        'Talibon': ['San Jose', 'Poblacion', 'Bagacay', 'Barangay 1', 'Barangay 2'],
        'Tagbilaran City': ['Bool', 'Booy', 'Cabawan', 'Cogon', 'Dampas'],
        'Carmen': ['Alegria', 'Buenos Aires', 'Katipunan', 'La Paz', 'Poblacion']
    }
};

function goBack() {
    // Close this page and return to previous view
    if (window.parent && window.parent.closeAddressesPage) {
        window.parent.closeAddressesPage();
    } else if (window.closeAddressesPage) {
        window.closeAddressesPage();
    } else {
        window.history.back();
    }
}

function showAddAddressPage() {
    // Create and show the add address page
    const addAddressPage = document.createElement('div');
    addAddressPage.id = 'addAddressPageOverlay';
    addAddressPage.className = 'fixed inset-0 z-[400] bg-white';
    
    addAddressPage.innerHTML = `
        <div class="min-h-screen bg-white">
            <!-- Header -->
            <div class="sticky top-0 z-50 bg-white border-b border-gray-200">
                <div class="flex items-center justify-center px-4 py-4 relative">
                    <button onclick="closeAddAddressPage()" class="absolute left-4 p-2 rounded-full hover:bg-gray-100 transition-all">
                        <i class="fas fa-chevron-left text-xl text-black"></i>
                    </button>
                    <h1 class="text-xl font-bold text-black">Add new address</h1>
                </div>
            </div>
            
            <!-- Content -->
            <div class="px-4 py-6">
                <!-- Contact Information -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-black mb-4">Contact information</h2>
                    
                    <!-- Full Name -->
                    <div class="mb-4">
                        <input type="text" placeholder="Full name" 
                               class="w-full p-4 border border-gray-300 rounded-lg text-black placeholder-gray-400 focus:outline-none focus:border-pink-400">
                    </div>
                    
                    <!-- Phone Number -->
                    <div class="mb-4">
                        <div class="flex">
                            <div class="flex items-center bg-gray-100 px-3 rounded-l-lg border border-r-0 border-gray-300">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRkZGRkZGIi8+CjxyZWN0IHdpZHRoPSIyNCIgaGVpZ2h0PSIxMiIgZmlsbD0iIzAwNzlGRiIvPgo8cmVjdCB5PSIxMiIgd2lkdGg9IjI0IiBoZWlnaHQ9IjEyIiBmaWxsPSIjRkYwMDAwIi8+Cjwvc3ZnPgo=" 
                                     alt="PH" class="w-6 h-4 mr-2">
                                <span class="text-black font-medium">+63</span>
                                <i class="fas fa-chevron-down text-gray-400 ml-2"></i>
                            </div>
                            <input type="tel" placeholder="Phone number" 
                                   class="flex-1 p-4 border border-gray-300 rounded-r-lg text-black placeholder-gray-400 focus:outline-none focus:border-pink-400">
                        </div>
                    </div>
                </div>
                
                <!-- Address Information -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-black mb-4">Address information</h2>
                    
                    <!-- Country -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between p-4 border border-gray-300 rounded-lg bg-gray-50">
                            <span class="text-black">Philippines</span>
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- Region -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between p-4 border border-gray-300 rounded-lg cursor-pointer" onclick="showProvinceSelector()">
                            <span id="regionText" class="text-gray-400">Select region</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- Address Details -->
                    <div class="mb-4">
                        <textarea placeholder="Address details (House number, street, landmark)" 
                                  rows="3"
                                  class="w-full p-4 border border-gray-300 rounded-lg text-black placeholder-gray-400 focus:outline-none focus:border-pink-400 resize-none"></textarea>
                    </div>
                </div>
                
                <!-- Settings -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-black mb-4">Settings</h2>
                    
                    <!-- Set as Default -->
                    <div class="flex items-center justify-between p-4 border border-gray-300 rounded-lg">
                        <span class="text-black">Set as default</span>
                        <div class="relative">
                            <input type="checkbox" id="defaultToggle" class="sr-only">
                            <label for="defaultToggle" class="flex items-center cursor-pointer">
                                <div class="w-12 h-6 bg-gray-300 rounded-full relative transition-colors duration-200">
                                    <div class="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 transition-transform duration-200 transform"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Privacy Policy -->
                <div class="mb-6">
                    <p class="text-gray-500 text-sm text-center">
                        By clicking Save, you acknowledge that you have read the 
                        <span class="text-black font-medium">Privacy Policy</span>.
                    </p>
                </div>
                
                <!-- Save Button -->
                <button onclick="saveAddress()" 
                        class="w-full bg-pink-400 text-white py-4 rounded-lg font-medium text-lg hover:bg-pink-500 transition-all">
                    Save
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(addAddressPage);
    
    // Add toggle functionality
    const toggle = addAddressPage.querySelector('#defaultToggle');
    const toggleBg = addAddressPage.querySelector('#defaultToggle + label > div');
    const toggleCircle = addAddressPage.querySelector('#defaultToggle + label > div > div');
    
    toggle.addEventListener('change', function() {
        if (this.checked) {
            toggleBg.classList.remove('bg-gray-300');
            toggleBg.classList.add('bg-pink-400');
            toggleCircle.classList.add('translate-x-6');
        } else {
            toggleBg.classList.remove('bg-pink-400');
            toggleBg.classList.add('bg-gray-300');
            toggleCircle.classList.remove('translate-x-6');
        }
    });
}

function closeAddAddressPage() {
    const addAddressPage = document.getElementById('addAddressPageOverlay');
    if (addAddressPage) {
        addAddressPage.remove();
    }
}

function saveAddress() {
    // Here you would implement the save functionality
    alert('Address saved successfully!');
    closeAddAddressPage();
}

function showProvinceSelector() {
    const modal = document.createElement('div');
    modal.id = 'provinceModal';
    modal.className = 'fixed inset-0 z-[500] bg-black bg-opacity-50 flex items-end';

    const provinces = Object.keys(philippinesData);
    const alphabetIndex = {};

    provinces.forEach(province => {
        const firstLetter = province.charAt(0).toUpperCase();
        if (!alphabetIndex[firstLetter]) {
            alphabetIndex[firstLetter] = [];
        }
        alphabetIndex[firstLetter].push(province);
    });

    modal.innerHTML = `
        <div class="bg-white w-full max-h-[80vh] rounded-t-3xl overflow-hidden">
            <!-- Header -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200">
                <h2 class="text-xl font-bold text-black">Select Province</h2>
                <button onclick="closeModal('provinceModal')" class="p-2 rounded-full hover:bg-gray-100">
                    <i class="fas fa-times text-xl text-black"></i>
                </button>
            </div>

            <!-- Content -->
            <div class="flex">
                <!-- Province List -->
                <div class="flex-1 overflow-y-auto max-h-[60vh]">
                    <div class="p-4">
                        <div class="border-b border-black pb-2 mb-4">
                            <span class="font-semibold text-black">Province</span>
                        </div>
                        ${Object.keys(alphabetIndex).sort().map(letter => `
                            <div class="mb-4">
                                <div class="text-lg font-bold text-black mb-2">${letter}</div>
                                ${alphabetIndex[letter].map(province => `
                                    <div class="py-3 cursor-pointer hover:bg-gray-100 text-black" onclick="selectProvince('${province}')">
                                        ${province}
                                    </div>
                                `).join('')}
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- Alphabet Index -->
                <div class="w-8 bg-gray-50 flex flex-col items-center justify-center py-4">
                    ${Object.keys(alphabetIndex).sort().map(letter => `
                        <div class="text-xs text-red-500 py-1">${letter}</div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function selectProvince(province) {
    selectedProvince = province;
    closeModal('provinceModal');

    // Update the region text to show selected province
    document.getElementById('regionText').textContent = province;
    document.getElementById('regionText').classList.remove('text-gray-400');
    document.getElementById('regionText').classList.add('text-black');

    // Automatically show municipality selector
    setTimeout(() => showMunicipalitySelector(), 300);
}

function showMunicipalitySelector() {
    if (!selectedProvince) return;

    const modal = document.createElement('div');
    modal.id = 'municipalityModal';
    modal.className = 'fixed inset-0 z-[500] bg-black bg-opacity-50 flex items-end';

    const municipalities = Object.keys(philippinesData[selectedProvince] || {});

    modal.innerHTML = `
        <div class="bg-white w-full max-h-[80vh] rounded-t-3xl overflow-hidden">
            <!-- Header -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200">
                <div class="flex items-center space-x-4">
                    <button onclick="goBackToProvince()" class="p-2 rounded-full hover:bg-gray-100">
                        <i class="fas fa-chevron-left text-xl text-black"></i>
                    </button>
                    <h2 class="text-xl font-bold text-black">Select Municipality</h2>
                </div>
                <button onclick="closeModal('municipalityModal')" class="p-2 rounded-full hover:bg-gray-100">
                    <i class="fas fa-times text-xl text-black"></i>
                </button>
            </div>

            <!-- Breadcrumb -->
            <div class="px-4 py-2 bg-gray-50 border-b border-gray-200">
                <div class="flex items-center space-x-2 text-sm">
                    <span class="text-gray-500">${selectedProvince}</span>
                    <span class="text-black font-semibold border-b-2 border-black pb-1">Municipality</span>
                </div>
            </div>

            <!-- Content -->
            <div class="overflow-y-auto max-h-[60vh] p-4">
                ${municipalities.length > 0 ? municipalities.map(municipality => `
                    <div class="py-3 cursor-pointer hover:bg-gray-100 text-black border-b border-gray-100" onclick="selectMunicipality('${municipality}')">
                        ${municipality}
                    </div>
                `).join('') : '<div class="text-gray-500 text-center py-8">No municipalities available</div>'}
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function selectMunicipality(municipality) {
    selectedMunicipality = municipality;
    closeModal('municipalityModal');

    // Update the region text to show province and municipality
    document.getElementById('regionText').textContent = `${selectedProvince}, ${municipality}`;

    // Automatically show barangay selector
    setTimeout(() => showBarangaySelector(), 300);
}

function showBarangaySelector() {
    if (!selectedProvince || !selectedMunicipality) return;

    const modal = document.createElement('div');
    modal.id = 'barangayModal';
    modal.className = 'fixed inset-0 z-[500] bg-black bg-opacity-50 flex items-end';

    const barangays = philippinesData[selectedProvince]?.[selectedMunicipality] || [];

    modal.innerHTML = `
        <div class="bg-white w-full max-h-[80vh] rounded-t-3xl overflow-hidden">
            <!-- Header -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200">
                <div class="flex items-center space-x-4">
                    <button onclick="goBackToMunicipality()" class="p-2 rounded-full hover:bg-gray-100">
                        <i class="fas fa-chevron-left text-xl text-black"></i>
                    </button>
                    <h2 class="text-xl font-bold text-black">Select Barangay</h2>
                </div>
                <button onclick="closeModal('barangayModal')" class="p-2 rounded-full hover:bg-gray-100">
                    <i class="fas fa-times text-xl text-black"></i>
                </button>
            </div>

            <!-- Breadcrumb -->
            <div class="px-4 py-2 bg-gray-50 border-b border-gray-200">
                <div class="flex items-center space-x-2 text-sm">
                    <span class="text-gray-500">${selectedProvince}</span>
                    <span class="text-gray-500">${selectedMunicipality}</span>
                    <span class="text-black font-semibold border-b-2 border-black pb-1">Barangay</span>
                </div>
            </div>

            <!-- Content -->
            <div class="overflow-y-auto max-h-[60vh] p-4">
                ${barangays.length > 0 ? barangays.map(barangay => `
                    <div class="py-3 cursor-pointer hover:bg-gray-100 text-black border-b border-gray-100" onclick="selectBarangay('${barangay}')">
                        ${barangay}
                    </div>
                `).join('') : '<div class="text-gray-500 text-center py-8">No barangays available</div>'}
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function selectBarangay(barangay) {
    selectedBarangay = barangay;
    closeModal('barangayModal');

    // Update the region text to show full address
    document.getElementById('regionText').textContent = `${selectedProvince}, ${selectedMunicipality}, ${barangay}`;
}

function goBackToProvince() {
    closeModal('municipalityModal');
    setTimeout(() => showProvinceSelector(), 300);
}

function goBackToMunicipality() {
    closeModal('barangayModal');
    setTimeout(() => showMunicipalitySelector(), 300);
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.remove();
    }
}

// Make functions globally available
window.goBack = goBack;
window.showAddAddressPage = showAddAddressPage;
window.closeAddAddressPage = closeAddAddressPage;
window.saveAddress = saveAddress;
window.showProvinceSelector = showProvinceSelector;
window.selectProvince = selectProvince;
window.selectMunicipality = selectMunicipality;
window.selectBarangay = selectBarangay;
window.goBackToProvince = goBackToProvince;
window.goBackToMunicipality = goBackToMunicipality;
window.closeModal = closeModal;
