let currentFilter = 'live';
        
        // Tab Navigation
        const navButtons = document.querySelectorAll('.nav-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        navButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');

                // Special handling for profile tab - show login modal
                if (targetTab === 'profile') {
                    showAuthModal();
                    return; // Don't proceed with normal tab switching
                }

                // Update nav button states
                navButtons.forEach(btn => {
                    if (btn.getAttribute('data-tab') === targetTab) {
                        btn.classList.remove('text-gray-400');
                        btn.classList.add('text-white');
                    } else {
                        btn.classList.remove('text-white');
                        btn.classList.add('text-gray-400');
                    }
                });

                // Show/hide tab content
                tabContents.forEach(content => {
                    if (content.id === targetTab + '-content') {
                        content.classList.remove('hidden');
                        // Add home-tab-active class for home tab
                        if (targetTab === 'home') {
                            content.classList.add('home-tab-active');
                            document.body.classList.add('home-tab-active');
                        }
                    } else {
                        content.classList.add('hidden');
                        content.classList.remove('home-tab-active');
                    }
                });

                // Remove home-tab-active from body if not home tab
                if (targetTab !== 'home') {
                    document.body.classList.remove('home-tab-active');
                }

                // Update bid/tip button states based on current filter
                if (targetTab === 'home') {
                    updateButtonStates();
                }
            });
        });
        
        // Navigation filter buttons (only in home tab)
        const navFilterButtons = document.querySelectorAll('.nav-filter-btn');
        
        navFilterButtons.forEach(button => {
            button.addEventListener('click', () => {
                const filter = button.getAttribute('data-filter');
                currentFilter = filter;

                // Update active button
                navFilterButtons.forEach(btn => {
                    btn.classList.remove('active');
                });
                button.classList.add('active');

                // Update button states
                updateButtonStates();

                // Filter content based on selection
                filterContent(filter);
            });
        });

        // Initialize Feed button as active (TikTok default)
        const feedNavButton = document.querySelector('.nav-filter-btn[data-filter="feed"]');
        if (feedNavButton) {
            feedNavButton.classList.add('active');
            currentFilter = 'feed'; // Set current filter to feed
        }
        
        function updateButtonStates() {
            const videos = document.querySelectorAll('#home-content .relative');

            videos.forEach(video => {
                const bidBtnIcon = video.querySelector('.bid-btn i');
                const tipBtnIcon = video.querySelector('.tip-btn i');
                const bidBtnElement = video.querySelector('.bid-btn');
                const category = video.dataset ? video.dataset.category : null;

                // Only proceed if we have the required elements
                if (!bidBtnIcon || !tipBtnIcon || !bidBtnElement) {
                    return;
                }

                const hasAuction = bidBtnElement.dataset && bidBtnElement.dataset.auction === 'true';

                // Reset colors
                bidBtnIcon.classList.remove('bid-available');
                tipBtnIcon.classList.remove('tip-available');

                // Update bid button - yellow if auction available
                if (hasAuction) {
                    bidBtnIcon.classList.add('bid-available');
                }

                // Update tip button - yellow if busking filter is active or video is busking category
                if (currentFilter === 'busking' || category === 'busking') {
                    tipBtnIcon.classList.add('tip-available');
                }
            });
        }
        
        function filterContent(filter) {
            const videos = document.querySelectorAll('#home-content .relative');
            
            videos.forEach(video => {
                const category = video.dataset.category;
                
                // Show/hide videos based on filter
                switch(filter) {
                    case 'friends':
                        video.style.display = 'block';
                        break;
                    case 'artists':
                        video.style.display = category === 'painting' ? 'block' : 'none';
                        break;
                    case 'feed':
                        video.style.display = 'block';
                        break;
                    case 'busking':
                        video.style.display = category === 'busking' ? 'block' : 'none';
                        break;
                    default:
                        video.style.display = 'block';
                }
            });
        }
        
        // Initialize button states
        document.addEventListener('DOMContentLoaded', function() {
            updateButtonStates();
        });
        
        // Shop Tabs (now handled by bottom slider functions)
        const marketplaceContent = document.getElementById('marketplace-content');
        const myshopContent = document.getElementById('myshop-content');

        // Initialize with marketplace visible by default
        if (marketplaceContent && myshopContent) {
            marketplaceContent.classList.remove('hidden');
            myshopContent.classList.add('hidden');
        }
        
        // Profile Tabs
        const videosTab = document.getElementById('videos-tab');
        const likedTab = document.getElementById('liked-tab');
        const bookmarksTab = document.getElementById('bookmarks-tab');
        
        [videosTab, likedTab, bookmarksTab].forEach(tab => {
            tab.addEventListener('click', () => {
                // Reset all tabs
                [videosTab, likedTab, bookmarksTab].forEach(t => {
                    t.classList.remove('tab-active');
                    t.classList.add('tab-inactive');
                });
                
                // Activate clicked tab
                tab.classList.remove('tab-inactive');
                tab.classList.add('tab-active');
            });
        });
        
        // Category filtering functionality
        const categoryButtons = document.querySelectorAll('.category-btn');
        categoryButtons.forEach(button => {
            button.addEventListener('click', () => {
                const category = button.getAttribute('data-category');
                
                // Don't filter if it's a dropdown button (services or instruments)
                if ((category === 'services' || category === 'instruments') && button.onclick) {
                    return;
                }
                
                // Update active button
                categoryButtons.forEach(btn => {
                    btn.classList.remove('bg-purple-600', 'text-white');
                    btn.classList.add('bg-gray-700', 'text-gray-300');
                });
                
                button.classList.remove('bg-gray-700', 'text-gray-300');
                button.classList.add('bg-purple-600', 'text-white');
                
                // Filter products
                const products = document.querySelectorAll('.product-card');
                products.forEach(product => {
                    const productCategory = product.getAttribute('data-category');
                    if (category === 'all' || productCategory === category) {
                        product.style.display = 'block';
                    } else {
                        product.style.display = 'none';
                    }
                });
            });
        });
        
        // Real-time interactive functions
        function toggleBrush(element) {
            const icon = element.querySelector('i');
            const countElement = element.parentElement.querySelector('.brush-count');
            let count = parseInt(element.dataset.count);
            
            if (icon.classList.contains('text-pink-500')) {
                // Unlike
                count--;
                icon.classList.remove('text-pink-500');
                icon.classList.add('text-gray-400');
                element.dataset.liked = 'false';
            } else {
                // Like
                count++;
                icon.classList.remove('text-gray-400');
                icon.classList.add('text-pink-500');
                element.dataset.liked = 'true';
                
                // Animation effect
                icon.style.transform = 'scale(1.3)';
                setTimeout(() => {
                    icon.style.transform = 'scale(1)';
                }, 200);
            }
            
            element.dataset.count = count;
            countElement.textContent = formatCount(count);
        }
        
        function toggleComments(button) {
            const videoCard = button.closest('.relative');
            const panel = videoCard.querySelector('.comments-section');
            const navOverlay = document.querySelector('.home-only-nav');

            // Hide all other comment sections first
            document.querySelectorAll('.comments-section').forEach(p => {
                if (p !== panel) {
                    p.classList.remove('translate-x-0');
                    p.classList.add('translate-x-full');
                }
            });

            // Toggle current one
            if (panel.classList.contains('translate-x-full')) {
                // Opening comments - hide navigation overlay
                panel.classList.remove('translate-x-full');
                panel.classList.add('translate-x-0');
                if (navOverlay) {
                    navOverlay.style.opacity = '0';
                    navOverlay.style.pointerEvents = 'none';
                }
            } else {
                // Closing comments - show navigation overlay
                panel.classList.remove('translate-x-0');
                panel.classList.add('translate-x-full');
                if (navOverlay) {
                    navOverlay.style.opacity = '1';
                    navOverlay.style.pointerEvents = 'auto';
                }
            }
        }
        
        
        
        
        function addComment(button) {
            const input = button.previousElementSibling;
            const commentText = input.value.trim();
            
            if (commentText) {
                const commentsContainer = button.closest('.comments-section').querySelector('.overflow-y-auto');
                const newComment = document.createElement('div');
                newComment.className = 'flex items-start space-x-2';
                newComment.innerHTML = `
                    <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=30&h=30&fit=crop" alt="User" class="w-6 h-6 rounded-full">
                    <div>
                        <span class="text-white text-sm font-medium">@you</span>
                        <p class="text-gray-300 text-sm">${commentText}</p>
                    </div>
                `;
                
                commentsContainer.appendChild(newComment);
                input.value = '';
                
                // Update comment count
                const commentBtn = button.closest('.relative').querySelector('.comment-btn');
                const countElement = commentBtn.parentElement.querySelector('.comment-count');
                let count = parseInt(commentBtn.dataset.count);
                count++;
                commentBtn.dataset.count = count;
                countElement.textContent = formatCount(count);
                
                // Scroll to bottom
                commentsContainer.scrollTop = commentsContainer.scrollHeight;
            }
        }

        function closeComments(button) {
            const commentsPanel = button.closest('.comments-section');
            const navOverlay = document.querySelector('.home-only-nav');

            if (commentsPanel) {
                // Close the comments panel with slide animation
                commentsPanel.classList.remove('translate-x-0');
                commentsPanel.classList.add('translate-x-full');

                // Show navigation overlay
                if (navOverlay) {
                    navOverlay.style.opacity = '1';
                    navOverlay.style.pointerEvents = 'auto';
                }
            }
        }

        // Completely disable dragging/swiping on comments panels
        document.addEventListener('DOMContentLoaded', function() {
            const commentsSections = document.querySelectorAll('.comments-section');

            commentsSections.forEach(section => {
                // Override any transform changes that might be applied by touch gestures
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                            const target = mutation.target;
                            if (target.style.transform && target.style.transform.includes('translateX') &&
                                !target.style.transform.includes('translateX(0px)') &&
                                !target.style.transform.includes('translate3d(100%') &&
                                !target.style.transform.includes('translateX(100%)')) {
                                // Reset any unauthorized transform changes
                                target.style.transform = '';
                            }
                        }
                    });
                });

                observer.observe(section, {
                    attributes: true,
                    attributeFilter: ['style']
                });

                // Prevent all touch events that could cause dragging
                section.addEventListener('touchstart', function(e) {
                    // Allow touch events only on interactive elements
                    if (e.target.closest('button') || e.target.closest('input') || e.target.closest('textarea') || e.target.closest('.overflow-y-auto')) {
                        return; // Allow normal interaction
                    }
                    e.preventDefault();
                    e.stopPropagation();
                }, { passive: false });

                section.addEventListener('touchmove', function(e) {
                    // Allow scrolling only in the comments area
                    if (e.target.closest('.overflow-y-auto')) {
                        return; // Allow scrolling
                    }
                    e.preventDefault();
                    e.stopPropagation();
                }, { passive: false });

                section.addEventListener('touchend', function(e) {
                    if (!e.target.closest('button') && !e.target.closest('input') && !e.target.closest('textarea') && !e.target.closest('.overflow-y-auto')) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                }, { passive: false });

                // Prevent mouse dragging as well
                section.addEventListener('mousedown', function(e) {
                    if (!e.target.closest('button') && !e.target.closest('input') && !e.target.closest('textarea') && !e.target.closest('.overflow-y-auto')) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                });

                section.addEventListener('mousemove', function(e) {
                    if (!e.target.closest('.overflow-y-auto')) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                });

                // Prevent any gesture events
                section.addEventListener('gesturestart', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }, { passive: false });

                section.addEventListener('gesturechange', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }, { passive: false });

                section.addEventListener('gestureend', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }, { passive: false });
            });

            // Global gesture prevention for the entire document
            // This prevents any swipe gestures that might interfere with comments
            document.addEventListener('touchstart', function(e) {
                // Check if touch is on video cards area where comments could be triggered
                const videoCard = e.target.closest('.relative');
                if (videoCard && videoCard.querySelector('.comments-section')) {
                    // Only allow touches on specific interactive elements
                    if (!e.target.closest('button') &&
                        !e.target.closest('input') &&
                        !e.target.closest('textarea') &&
                        !e.target.closest('.overflow-y-auto') &&
                        !e.target.closest('video') &&
                        !e.target.closest('.action-btn')) {
                        // Prevent any potential swipe gesture
                        e.preventDefault();
                        e.stopPropagation();
                    }
                }
            }, { passive: false });

            document.addEventListener('touchmove', function(e) {
                const videoCard = e.target.closest('.relative');
                if (videoCard && videoCard.querySelector('.comments-section')) {
                    // Only allow scrolling in comments area and video interaction
                    if (!e.target.closest('.overflow-y-auto') &&
                        !e.target.closest('video')) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                }
            }, { passive: false });

            // Prevent any pan gestures that could trigger comments
            document.addEventListener('pointerdown', function(e) {
                const videoCard = e.target.closest('.relative');
                if (videoCard && videoCard.querySelector('.comments-section')) {
                    if (!e.target.closest('button') &&
                        !e.target.closest('input') &&
                        !e.target.closest('textarea') &&
                        !e.target.closest('.overflow-y-auto') &&
                        !e.target.closest('video') &&
                        !e.target.closest('.action-btn')) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                }
            }, { passive: false });

            document.addEventListener('pointermove', function(e) {
                const videoCard = e.target.closest('.relative');
                if (videoCard && videoCard.querySelector('.comments-section')) {
                    if (!e.target.closest('.overflow-y-auto') &&
                        !e.target.closest('video')) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                }
            }, { passive: false });


        });

        function shareVideo(element) {
            document.getElementById('shareModal').classList.remove('hidden');
            
            // Update share count
            const countElement = element.parentElement.querySelector('.share-count');
            let count = parseInt(element.dataset.count);
            count++;
            element.dataset.count = count;
            countElement.textContent = formatCount(count);
        }
        
        function closeShareModal() {
            document.getElementById('shareModal').classList.add('hidden');
        }
        
        function shareToSocial(platform) {
            const link = document.getElementById('shareLink').value;
            let shareUrl = '';
            
            switch(platform) {
                case 'facebook':
                    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(link)}`;
                    break;
                case 'twitter':
                    shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(link)}&text=Check out this amazing art!`;
                    break;
                case 'instagram':
                    // Instagram doesn't support direct URL sharing, so copy to clipboard
                    copyLink();
                    showNotification('Link copied! Paste it in Instagram');
                    return;
            }
            
            if (shareUrl) {
                window.open(shareUrl, '_blank', 'width=600,height=400');
            }
            closeShareModal();
        }
        
        function copyLink() {
            const input = document.getElementById('shareLink');
            input.select();
            document.execCommand('copy');
            showNotification('Link copied to clipboard!');
        }
        
        function showTipModal(element) {
            document.getElementById('tipModal').classList.remove('hidden');
        }
        
        function closeTipModal() {
            document.getElementById('tipModal').classList.add('hidden');
        }
        
        function selectTipAmount(amount) {
            document.getElementById('customTipAmount').value = amount;
            
            // Visual feedback
            document.querySelectorAll('.tip-amount').forEach(btn => {
                btn.classList.remove('bg-pink-500');
                btn.classList.add('bg-gray-700');
            });
            event.target.classList.remove('bg-gray-700');
            event.target.classList.add('bg-pink-500');
        }
        
        function sendTip() {
            const amount = document.getElementById('customTipAmount').value;
            if (amount && amount > 0) {
                showNotification(`Tip of ₱${amount} sent successfully!`);
                closeTipModal();
            } else {
                alert('Please enter a valid tip amount');
            }
        }
        
        function showBidModal(element) {
            const isAuction = element.dataset.auction === 'true';
            if (isAuction) {
                document.getElementById('bidModal').classList.remove('hidden');
            } else {
                showTipModal(element);
            }
        }
        
        function closeBidModal() {
            document.getElementById('bidModal').classList.add('hidden');
        }
        
        function placeBid() {
            const bidAmount = document.getElementById('bidAmount').value;
            const currentBid = parseInt(document.getElementById('currentBidAmount').textContent.replace(/[₱,]/g, ''));
            
            if (bidAmount && parseInt(bidAmount) > currentBid) {
                document.getElementById('currentBidAmount').textContent = `₱${parseInt(bidAmount).toLocaleString()}`;
                showNotification(`Bid of ₱${parseInt(bidAmount).toLocaleString()} placed successfully!`);
                closeBidModal();
            } else {
                alert(`Please enter a bid higher than ₱${currentBid.toLocaleString()}`);
            }
        }
        
        function toggleFollow(button) {
            if (button.textContent.trim() === 'Follow') {
                button.textContent = 'Following';
                button.classList.remove('from-pink-500', 'to-purple-600');
                button.classList.add('bg-gray-600');
                showNotification('Now following this user!');
            } else {
                button.textContent = 'Follow';
                button.classList.remove('bg-gray-600');
                button.classList.add('from-pink-500', 'to-purple-600');
            }
        }
        
        function formatCount(count) {
            if (count >= 1000000) {
                return (count / 1000000).toFixed(1) + 'M';
            } else if (count >= 1000) {
                return (count / 1000).toFixed(1) + 'K';
            }
            return count.toString();
        }
        
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'fixed top-24 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-6 py-3 rounded-full z-50 transition-all border border-gray-600';
            notification.innerHTML = `<i class="fas fa-check mr-2 text-green-400"></i>${message}`;
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translate(-50%, 0)';
            }, 100);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translate(-50%, -100px)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }
        
        // Add CSS for animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .brush-btn i {
                transition: all 0.2s ease;
            }
            
            .brush-btn:hover i {
                transform: scale(1.1);
            }
        `;
        document.head.appendChild(style);
        
        function viewLinkedProducts(element) {
            document.getElementById('linkedProductsModal').classList.remove('hidden');
        }
        
        function closeLinkedProductsModal() {
            document.getElementById('linkedProductsModal').classList.add('hidden');
        }
        
        // Services dropdown functionality
        // Track dropdown state
        let servicesDropdownOpen = false;
        let instrumentsDropdownOpen = false;
        let artServicesDropdownOpen = false;
        let musicServicesDropdownOpen = false;
        
        function toggleDropdown(button, dropdownId, stateVariable) {
            const dropdown = document.getElementById(dropdownId);
            if (!dropdown) return;

            const buttonRect = button.getBoundingClientRect();
            const screenWidth = window.innerWidth;

            // Close other dropdowns
            const dropdowns = [
                { id: 'servicesDropdown', state: 'servicesDropdownOpen', category: 'services' },
                { id: 'instrumentsDropdown', state: 'instrumentsDropdownOpen', category: 'instruments' },
                { id: 'artServicesDropdown', state: 'artServicesDropdownOpen', category: 'art-services' },
                { id: 'musicServicesDropdown', state: 'musicServicesDropdownOpen', category: 'music-services' }
            ];

            dropdowns.forEach(item => {
                if (item.id !== dropdownId) {
                    const otherDropdown = document.getElementById(item.id);
                    const otherBtn = document.querySelector(`[data-category="${item.category}"]`);
                    if (otherDropdown && otherBtn) {
                        otherDropdown.classList.add('hidden');
                        otherBtn.classList.remove('bg-purple-600', 'text-white');
                        otherBtn.classList.add('bg-gray-700', 'text-gray-300');
                        // Reset state variables
                        if (item.state === 'servicesDropdownOpen') servicesDropdownOpen = false;
                        else if (item.state === 'instrumentsDropdownOpen') instrumentsDropdownOpen = false;
                        else if (item.state === 'artServicesDropdownOpen') artServicesDropdownOpen = false;
                        else if (item.state === 'musicServicesDropdownOpen') musicServicesDropdownOpen = false;
                    }
                }
            });

            // Toggle current dropdown
            dropdown.classList.toggle('hidden');
            const isOpen = !dropdown.classList.contains('hidden');

            if (isOpen) {
                button.classList.remove('bg-gray-700', 'text-gray-300');
                button.classList.add('bg-purple-600', 'text-white');

                // Position dropdown
                dropdown.style.position = 'fixed';
                dropdown.style.top = (buttonRect.bottom + 8) + 'px';
                dropdown.style.left = buttonRect.left + 'px';

                // Check for overflow and adjust
                setTimeout(() => {
                    const dropdownRect = dropdown.getBoundingClientRect();
                    if (dropdownRect.right > screenWidth) {
                        dropdown.style.left = (screenWidth - dropdownRect.width - 8) + 'px';
                    }
                    if (dropdownRect.left < 0) {
                        dropdown.style.left = '8px';
                    }
                }, 10);
            } else {
                button.classList.remove('bg-purple-600', 'text-white');
                button.classList.add('bg-gray-700', 'text-gray-300');
            }

            // Update state
            if (stateVariable === 'services') {
                servicesDropdownOpen = isOpen;
            } else if (stateVariable === 'instruments') {
                instrumentsDropdownOpen = isOpen;
            } else if (stateVariable === 'art-services') {
                artServicesDropdownOpen = isOpen;
            } else if (stateVariable === 'music-services') {
                musicServicesDropdownOpen = isOpen;
            }
        }
        
        function toggleServicesDropdown() {
            const button = document.querySelector('[data-category="services"]');
            toggleDropdown(button, 'servicesDropdown', 'services');
        }
        
        function toggleInstrumentsDropdown() {
            const button = document.querySelector('[data-category="instruments"]');
            toggleDropdown(button, 'instrumentsDropdown', 'instruments');
        }

        function toggleArtServicesDropdown() {
            const button = document.querySelector('[data-category="art-services"]');
            toggleDropdown(button, 'artServicesDropdown', 'art-services');
        }

        function toggleMusicServicesDropdown() {
            const button = document.querySelector('[data-category="music-services"]');
            toggleDropdown(button, 'musicServicesDropdown', 'music-services');
        }
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            // Services dropdown
            const servicesDropdown = document.getElementById('servicesDropdown');
            const servicesButton = event.target.closest('[onclick="toggleServicesDropdown()"]');
            const servicesBtn = document.querySelector('[data-category="services"]');
            if (servicesDropdown && !servicesButton && !servicesDropdown.contains(event.target)) {
                servicesDropdown.classList.add('hidden');
                if (servicesBtn) {
                    servicesBtn.classList.remove('bg-purple-600', 'text-white');
                    servicesBtn.classList.add('bg-gray-700', 'text-gray-300');
                }
                servicesDropdownOpen = false;
            }

            // Instruments dropdown
            const instrumentsDropdown = document.getElementById('instrumentsDropdown');
            const instrumentsButton = event.target.closest('[onclick="toggleInstrumentsDropdown()"]');
            const instrumentsBtn = document.querySelector('[data-category="instruments"]');

            if (instrumentsDropdown && !instrumentsButton && !instrumentsDropdown.contains(event.target)) {
                instrumentsDropdown.classList.add('hidden');
                if (instrumentsBtn) {
                    instrumentsBtn.classList.remove('bg-purple-600', 'text-white');
                    instrumentsBtn.classList.add('bg-gray-700', 'text-gray-300');
                }
                instrumentsDropdownOpen = false;
            }

            // Art Services dropdown
            const artServicesDropdown = document.getElementById('artServicesDropdown');
            const artServicesButton = event.target.closest('[onclick="toggleArtServicesDropdown()"]');
            const artServicesBtn = document.querySelector('[data-category="art-services"]');

            if (artServicesDropdown && !artServicesButton && !artServicesDropdown.contains(event.target)) {
                artServicesDropdown.classList.add('hidden');
                if (artServicesBtn) {
                    artServicesBtn.classList.remove('bg-purple-600', 'text-white');
                    artServicesBtn.classList.add('bg-gray-700', 'text-gray-300');
                }
            }

            // Music Services dropdown
            const musicServicesDropdown = document.getElementById('musicServicesDropdown');
            const musicServicesButton = event.target.closest('[onclick="toggleMusicServicesDropdown()"]');
            const musicServicesBtn = document.querySelector('[data-category="music-services"]');

            if (musicServicesDropdown && !musicServicesButton && !musicServicesDropdown.contains(event.target)) {
                musicServicesDropdown.classList.add('hidden');
                if (musicServicesBtn) {
                    musicServicesBtn.classList.remove('bg-purple-600', 'text-white');
                    musicServicesBtn.classList.add('bg-gray-700', 'text-gray-300');
                }
            }
        });
        
        // When another category is selected, close dropdowns and remove highlights
        categoryButtons.forEach(button => {
            button.addEventListener('click', () => {
                const category = button.getAttribute('data-category');
                
                if (category !== 'services') {
                    const servicesBtn = document.querySelector('[data-category="services"]');
                    servicesBtn.classList.remove('bg-purple-600', 'text-white');
                    servicesBtn.classList.add('bg-gray-700', 'text-gray-300');
                    servicesDropdownOpen = false;
                    document.getElementById('servicesDropdown').classList.add('hidden');
                }
                
                if (category !== 'instruments') {
                    const instrumentsBtn = document.querySelector('[data-category="instruments"]');
                    instrumentsBtn.classList.remove('bg-purple-600', 'text-white');
                    instrumentsBtn.classList.add('bg-gray-700', 'text-gray-300');
                    instrumentsDropdownOpen = false;
                    document.getElementById('instrumentsDropdown').classList.add('hidden');
                }
            });
        });
        
        // Service subcategory filtering
        document.querySelectorAll('.service-subcategory').forEach(button => {
            button.addEventListener('click', function() {
                const subcategory = this.dataset.subcategory;
                const products = document.querySelectorAll('.product-card');
                
                products.forEach(product => {
                    if (product.dataset.subcategory === subcategory || subcategory === 'all') {
                        product.style.display = 'block';
                    } else {
                        product.style.display = 'none';
                    }
                });
                
                // Remove highlight from all main categories
                document.querySelectorAll('.category-btn').forEach(btn => {
                    btn.classList.remove('bg-purple-600', 'text-white');
                    btn.classList.add('bg-gray-700', 'text-gray-300');
                });
                // Highlight only Services
                const servicesBtn = document.querySelector('[data-category="services"]');
                servicesBtn.classList.remove('bg-gray-700', 'text-gray-300');
                servicesBtn.classList.add('bg-purple-600', 'text-white');
                // Close dropdown
                document.getElementById('servicesDropdown').classList.add('hidden');
                servicesDropdownOpen = false;
            });
        });
        
        // Instrument condition filtering (brand-new, second-hand)
        document.querySelectorAll('.instruments-subcategory').forEach(button => {
            button.addEventListener('click', function () {
                const subcategory = this.dataset.subcategory;
                const products = document.querySelectorAll('.product-card');

                products.forEach(product => {
                    if (product.dataset.subcategory === subcategory || subcategory === 'all') {
                        product.style.display = 'block';
                    } else {
                        product.style.display = 'none';
                    }
                });

                // Highlight Instruments
                document.querySelectorAll('.category-btn').forEach(btn => {
                    btn.classList.remove('bg-purple-600', 'text-white');
                    btn.classList.add('bg-gray-700', 'text-gray-300');
                });

                const instrumentsBtn = document.querySelector('[data-category="instruments"]');
                instrumentsBtn.classList.remove('bg-gray-700', 'text-gray-300');
                instrumentsBtn.classList.add('bg-purple-600', 'text-white');

                // Close condition dropdown
                document.getElementById('instrumentsConditionDropdown').classList.add('hidden');
                instrumentsDropdownOpen = false;
            });
        });

        
        // Luthier Service Modal Functions
        function openLuthierForm() {
            document.getElementById('luthierModal').classList.remove('hidden');
        }
        
        function closeLuthierForm() {
            document.getElementById('luthierModal').classList.add('hidden');
            // Clear all input fields in the modal
            document.getElementById('luthierServiceType').value = 'repair';
            document.getElementById('luthierPriceMin').value = '';
            document.getElementById('luthierPriceMax').value = '';
            document.getElementById('luthierDurationDays').value = '';
            document.getElementById('durationFormatted').textContent = '';
            document.getElementById('luthierLocation').value = '';
        }
        
        function submitLuthierService() {
            const type = document.getElementById('luthierServiceType').value;
            const priceMin = document.getElementById('luthierPriceMin').value;
            const priceMax = document.getElementById('luthierPriceMax').value;
            const duration = document.getElementById('luthierDuration').value;
            const location = document.getElementById('luthierLocation').value;
        
            if (!type || !priceMin || !priceMax || !duration || !location) {
                alert('Please fill in all fields.');
                return;
            }
        
            // Here you would handle the form submission (e.g., send to backend or show confirmation)
            alert(`Request submitted!\nType: ${type}\nPrice: ₱${priceMin} - ₱${priceMax}\nDuration: ${duration}\nLocation: ${location}`);
            closeLuthierForm();
        }
        
        function openLuthierListModal() {
            document.getElementById('luthierListModal').classList.remove('hidden');
        }
        
        function closeLuthierListModal() {
            document.getElementById('luthierListModal').classList.add('hidden');
        }
        
        // Chat Modal
        const chatModal = document.getElementById('chat-modal');
        const closeChatBtn = document.getElementById('close-chat');
        
        function openChat() {
            chatModal.classList.remove('hidden');
        }
        
        closeChatBtn.addEventListener('click', () => {
            chatModal.classList.add('hidden');
        });
        
        chatModal.addEventListener('click', (e) => {
            if (e.target === chatModal) {
                chatModal.classList.add('hidden');
            }
        });
        
        // Buy Button Actions
        // Remove global add-to-cart for all buy-btns
        // Only add to cart for non-luthier buy-btns
        
        // Remove or comment out this block:
        // document.querySelectorAll('.buy-btn').forEach(btn => {
        //     btn.addEventListener('click', (e) => {
        //         e.stopPropagation();
        //         // Create a simple notification
        //         const notification = document.createElement('div');
        //         notification.className = 'fixed top-24 left-1/2 transform -translate-x-1/2 bg-green-600 text-white px-6 py-3 rounded-full z-50 transition-all';
        //         notification.innerHTML = '<i class="fas fa-check mr-2"></i>Added to cart!';
        //         document.body.appendChild(notification);
        //         setTimeout(() => {
        //             notification.remove();
        //         }, 2000);
        //     });
        // });
        
        // Instead, use event delegation for buy-btns:
        document.addEventListener('click', function(e) {
            const buyBtn = e.target.closest('.buy-btn');
            if (!buyBtn) return;
            const luthierCard = buyBtn.closest('.product-card[data-subcategory="guitar-luthier"]');
            if (luthierCard) {
                openLuthierForm();
                e.preventDefault();
                e.stopPropagation();
                return;
            }
            // For other buy-btns, show add-to-cart notification as before
            // (copy the notification code here if needed)
            const notification = document.createElement('div');
            notification.className = 'fixed top-24 left-1/2 transform -translate-x-1/2 bg-green-600 text-white px-6 py-3 rounded-full z-50 transition-all';
            notification.innerHTML = '<i class="fas fa-check mr-2"></i>Added to cart!';
            document.body.appendChild(notification);
            setTimeout(() => {
                notification.remove();
            }, 2000);
        });
        
        // Follow Button Actions
        document.querySelectorAll('.follow-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                
                if (btn.innerHTML.includes('Follow')) {
                    btn.innerHTML = '<i class="fas fa-check mr-1"></i>Following';
                    btn.classList.add('bg-gray-600');
                    btn.classList.remove('follow-btn');
                } else {
                    btn.innerHTML = '<i class="fas fa-plus mr-1"></i>Follow';
                    btn.classList.remove('bg-gray-600');
                    btn.classList.add('follow-btn');
                }
            });
        });
        
        // Add Product Modal Functions
        function openAddProductModal() {
            document.getElementById('addProductModal').style.display = 'flex';
        }
        
        function closeAddProductModal() {
            document.getElementById('addProductModal').style.display = 'none';
        }
        
        function addProduct() {
            const title = document.getElementById('productTitle').value;
            const price = document.getElementById('productPrice').value;
            const category = document.getElementById('productCategory').value;
            const description = document.getElementById('productDescription').value;
            const imageFile = document.getElementById('productImage').files[0];
            const condition = document.getElementById('productCondition').value;
            
            if (!title || !price || !category) {
                alert('Please fill in all required fields');
                return;
            }
            if (category === 'instruments' && !condition) {
                alert('Please select the item condition');
                return;
            }
            // Create new product card
            const productsGrid = document.querySelector('#myshop-content .products-grid');
            const marketplaceGrid = document.querySelector('#marketplace-content > div.grid');
            const newProduct = document.createElement('div');
            newProduct.className = 'product-card';
            newProduct.setAttribute('data-category', category);
            if (category === 'instruments') {
                newProduct.setAttribute('data-condition', condition);
            }
            newProduct.style.cssText = 'background: #1a1a1a; border-radius: 12px; overflow: hidden; border: 1px solid #333;';
            // Handle image preview
            let imageHTML = '<div style="width: 100%; height: 120px; background: linear-gradient(45deg, #333, #555); display: flex; align-items: center; justify-content: center; color: #888;"><span>📷</span></div>';
            if (imageFile) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    newProduct.querySelector('.product-image').innerHTML = `<img src="${e.target.result}" style="width: 100%; height: 120px; object-fit: cover;">`;
                };
                reader.readAsDataURL(imageFile);
            }
            // Add condition display for instruments
            let conditionHTML = '';
            if (category === 'musical-instruments') {
                conditionHTML = `<span style=\"display:inline-block;padding:2px 8px;background:#333;color:#fff;border-radius:8px;font-size:11px;margin-bottom:6px;\">${condition === 'brand-new' ? 'Brand New' : 'Secondhand'}</span>`;
            }
            newProduct.innerHTML = `
                <div style="position: relative;" class="product-image">
                    ${imageHTML}
                    <div style="position: absolute; top: 8px; right: 8px; background: rgba(0,0,0,0.7); border-radius: 12px; padding: 4px 8px;">
                        <span style="color: white; font-size: 10px; font-weight: bold;">NEW</span>
                    </div>
                </div>
                <div style="padding: 12px;">
                    ${conditionHTML}
                    <h4 style="color: white; font-size: 14px; margin: 0 0 8px 0; font-weight: 600;">${title}</h4>
                    <p style="color: #ff6b6b; font-size: 16px; font-weight: bold; margin: 0 0 8px 0;">₱${parseFloat(price).toLocaleString()}</p>
                    ${description ? `<p style=\"color: #888; font-size: 11px; margin: 0 0 8px 0;\">${description}</p>` : ''}
                    <div style="display: flex; gap: 6px;">
                        <button style="flex: 1; padding: 8px; border: none; border-radius: 6px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; font-size: 11px; font-weight: bold; cursor: pointer;">Buy Now</button>
                        <button onclick="openChat()" style="padding: 8px; border: 1px solid #333; border-radius: 6px; background: transparent; color: white; font-size: 11px; cursor: pointer;">💬</button>
                    </div>
                </div>
            `;
            productsGrid.appendChild(newProduct);
            // Also add to marketplace
            if (marketplaceGrid) {
                const newProductForMarketplace = newProduct.cloneNode(true);
                marketplaceGrid.appendChild(newProductForMarketplace);
            }
            // Update stats
            const totalProducts = document.querySelectorAll('#myshop-content .product-card').length;
            document.querySelector('#myshop-content .stat-number').textContent = totalProducts;
            // Clear form and close modal
            document.getElementById('addProductForm').reset();
            closeAddProductModal();
            alert('Product added successfully!');
        }
        
        // Camera and Video Recording Functions
        let currentUser = 'user123';
        let isRecording = false;
        let mediaRecorder;
        let recordingStream;
        let recordingStartTime;
        let recordingTimer;
        let currentCamera = 'user'; // 'user' for front, 'environment' for back
        
        function startRecording() {
            document.getElementById('cameraModal').classList.remove('hidden');
            initializeCamera();
        }
        
        async function initializeCamera() {
            try {
                // Check if getUserMedia is supported
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('getUserMedia is not supported in this browser');
                }

                // Try with ideal constraints first
                let constraints = {
                    video: {
                        facingMode: currentCamera,
                        width: { ideal: 1080 },
                        height: { ideal: 1920 }
                    },
                    audio: true
                };

                try {
                    recordingStream = await navigator.mediaDevices.getUserMedia(constraints);
                } catch (detailedError) {
                    console.warn('Failed with ideal constraints, trying basic constraints:', detailedError);

                    // Fallback to basic constraints
                    constraints = {
                        video: {
                            facingMode: currentCamera
                        },
                        audio: true
                    };

                    try {
                        recordingStream = await navigator.mediaDevices.getUserMedia(constraints);
                    } catch (basicError) {
                        console.warn('Failed with basic constraints, trying video only:', basicError);

                        // Last fallback - video only
                        constraints = {
                            video: true
                        };
                        recordingStream = await navigator.mediaDevices.getUserMedia(constraints);
                    }
                }

                const video = document.getElementById('cameraPreview');
                video.srcObject = recordingStream;

                // Wait for video to be ready
                video.onloadedmetadata = () => {
                    video.play().catch(e => console.error('Error playing video:', e));
                };

            } catch (error) {
                console.error('Error accessing camera:', error);
                let errorMessage = 'Unable to access camera. ';

                if (error.name === 'NotAllowedError') {
                    errorMessage += 'Please allow camera permissions and try again.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage += 'No camera device found.';
                } else if (error.name === 'NotReadableError') {
                    errorMessage += 'Camera is already in use by another application.';
                } else {
                    errorMessage += 'Please check your camera permissions and try again.';
                }

                alert(errorMessage);
                closeCameraModal();
            }
        }
        
        function closeCameraModal() {
            if (recordingStream) {
                recordingStream.getTracks().forEach(track => track.stop());
            }
            if (isRecording) {
                stopRecording();
            }
            document.getElementById('cameraModal').classList.add('hidden');
        }
        
        async function flipCamera() {
            currentCamera = currentCamera === 'user' ? 'environment' : 'user';
            if (recordingStream) {
                recordingStream.getTracks().forEach(track => track.stop());
            }
            await initializeCamera();
        }
        
        function toggleRecording() {
            if (isRecording) {
                stopRecording();
            } else {
                startVideoRecording();
            }
        }
        
        function startVideoRecording() {
            if (!recordingStream) return;
        
            const recordBtn = document.getElementById('recordBtn');
            const timer = document.getElementById('recordingTimer');
            
            recordBtn.classList.add('recording');
            timer.style.display = 'block';
            
            mediaRecorder = new MediaRecorder(recordingStream);
            const chunks = [];
            
            mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    chunks.push(event.data);
                }
            };
            
            mediaRecorder.onstop = () => {
                const blob = new Blob(chunks, { type: 'video/webm' });
                // Process recorded video
                processRecordedVideo(blob);
            };
            
            mediaRecorder.start();
            isRecording = true;
            recordingStartTime = Date.now();
            
            recordingTimer = setInterval(updateRecordingTimer, 1000);
        }
        
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                
                const recordBtn = document.getElementById('recordBtn');
                const timer = document.getElementById('recordingTimer');
                
                recordBtn.classList.remove('recording');
                timer.style.display = 'none';
                
                clearInterval(recordingTimer);
            }
        }
        
        function updateRecordingTimer() {
            const elapsed = Math.floor((Date.now() - recordingStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            document.getElementById('recordingTimer').textContent = `${minutes}:${seconds}`;
        }
        
        function processRecordedVideo(blob) {
            // Create video URL and show preview
            const videoUrl = URL.createObjectURL(blob);
            console.log('Video recorded:', videoUrl);
            alert('Video recorded successfully! Processing...');
            closeCameraModal();
        }
        
        function addEffect() {
            alert('Effects feature coming soon!');
        }
        
        function addMusic() {
            alert('Music feature coming soon!');
        }
        
        function uploadVideo() {
            document.getElementById('galleryModal').classList.remove('hidden');
        }
        
        function closeGalleryModal() {
            document.getElementById('galleryModal').classList.add('hidden');
            document.getElementById('uploadPreview').style.display = 'none';
            document.getElementById('videoUpload').value = '';
        }
        
        function handleVideoUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const video = document.getElementById('uploadedVideo');
                const preview = document.getElementById('uploadPreview');
                
                video.src = URL.createObjectURL(file);
                preview.style.display = 'block';
            }
        }
        
        function processUploadedVideo() {
            const video = document.getElementById('uploadedVideo');
            console.log('Processing uploaded video:', video.src);
            alert('Video uploaded successfully! Processing...');
            closeGalleryModal();
        }
        
        function goLive() {
            alert('Live streaming feature will be implemented here');
        }
        
        // Authentication functions
        function showAuthModal() {
            document.getElementById('authModal').classList.remove('hidden');
        }
        
        function closeAuthModal() {
            document.getElementById('authModal').classList.add('hidden');
        }
        
        function showLoginForm() {
            document.getElementById('signupForm').classList.add('hidden');
            document.getElementById('loginForm').classList.remove('hidden');
        }
        
        function showSignupForm() {
            document.getElementById('loginForm').classList.add('hidden');
            document.getElementById('signupForm').classList.remove('hidden');
        }
        
        function login() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                alert('Please enter both email and password');
                return;
            }
            
            // Simulate login
            alert('Login successful!');
            closeAuthModal();
            
            // Update UI to show logged in state
            document.querySelector('.header-user-section').innerHTML = `
                <div class="flex items-center space-x-2">
                    <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&fit=crop" 
                         alt="Profile" class="w-8 h-8 rounded-full">
                    <span class="text-sm font-medium">@username</span>
                </div>
            `;
        }
        
        function signup() {
            const username = document.getElementById('signupUsername').value;
            const email = document.getElementById('signupEmail').value;
            const password = document.getElementById('signupPassword').value;
            const confirmPassword = document.getElementById('signupConfirmPassword').value;
            
            if (!username || !email || !password || !confirmPassword) {
                alert('Please fill all fields');
                return;
            }
            
            if (password !== confirmPassword) {
                alert('Passwords do not match');
                return;
            }
            
            // Simulate signup
            alert('Account created successfully! You can now log in.');
            showLoginForm();
        }
        
        function openMessagesPanel() {
            document.getElementById('messagesPanel').classList.remove('hidden');
        }

        function closeMessagesPanel() {
            document.getElementById('messagesPanel').classList.add('hidden');
        }
        
        function openChat(userId) {
            // Simulate opening a specific chat
            document.getElementById('activeChatUser').innerText = userId === 'user1' ? '@artista_maya' : '@creative_soul';
            
            // You would normally load chat messages here
        }
        
        // Search functionality
        function toggleSearch() {
            document.getElementById('searchOverlay').classList.toggle('hidden');
        }
        
        function closeSearch() {
            document.getElementById('searchOverlay').classList.add('hidden');
        }
        
        // Favorites collection
        function toggleFavorites() {
            document.getElementById('favoritesPanel').classList.toggle('hidden');
        }
        
        function closeFavorites() {
            document.getElementById('favoritesPanel').classList.add('hidden');
        }
        
        function addToFavorites(btn) {
            const icon = btn.querySelector('i');
            if (icon.classList.contains('text-white')) {
                icon.classList.remove('text-white');
                icon.classList.add('text-pink-500');
                alert('Added to favorites!');
            } else {
                icon.classList.remove('text-pink-500');
                icon.classList.add('text-white');
                alert('Removed from favorites!');
            }
        }
        
        function removeFromFavorites(btn) {
            // Get parent favorite item and remove it
            const item = btn.closest('.favorite-item');
            item.remove();
            alert('Removed from favorites!');
        }
        
        // Commission request system
        function openCommissionModal(artistId) {
            // Create and show commission request modal
            const modal = document.createElement('div');
            modal.id = 'commissionModal';
            modal.className = 'fixed inset-0 bg-black/90 z-[100] flex items-center justify-center';
            modal.innerHTML = `
                <div class="bg-gray-900 p-6 rounded-xl w-full max-w-md mx-4">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-bold">Request a Commission</h2>
                        <button onclick="closeCommissionModal()" class="p-1 rounded-full hover:bg-gray-700">
                            <i class="fas fa-times text-lg"></i>
                        </button>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-1">Commission Type</label>
                            <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                                <option value="painting">Painting</option>
                                <option value="digital">Digital Art</option>
                                <option value="sculpture">Sculpture</option>
                                <option value="music">Music</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Description</label>
                            <textarea class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500 h-24"></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Budget (₱)</label>
                            <input type="number" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Deadline</label>
                            <input type="date" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <button onclick="submitCommissionRequest()" class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-2 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all">
                            Submit Request
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }
        
        function closeCommissionModal() {
            const modal = document.getElementById('commissionModal');
            if (modal) modal.remove();
        }
        
        function submitCommissionRequest() {
            alert('Commission request sent! The artist will contact you soon.');
            closeCommissionModal();
        }
        
        // Payment integration
        function openPaymentModal(amount, description) {
            // Create and show payment modal
            const modal = document.createElement('div');
            modal.id = 'paymentModal';
            modal.className = 'fixed inset-0 bg-black/90 z-[100] flex items-center justify-center';
            modal.innerHTML = `
                <div class="bg-gray-900 p-6 rounded-xl w-full max-w-md mx-4">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-bold">Complete Payment</h2>
                        <button onclick="closePaymentModal()" class="p-1 rounded-full hover:bg-gray-700">
                            <i class="fas fa-times text-lg"></i>
                        </button>
                    </div>
                    
                    <div class="mb-6 p-4 bg-gray-800 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-sm">Amount</span>
                            <span class="text-lg font-bold">₱${amount}</span>
                        </div>
                        <div class="text-sm text-gray-400">${description}</div>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-1">Payment Method</label>
                            <div class="grid grid-cols-3 gap-3 mb-4">
                                <button class="p-3 bg-gray-800 rounded-lg border border-pink-500 flex items-center justify-center">
                                    <i class="fab fa-cc-visa text-2xl"></i>
                                </button>
                                <button class="p-3 bg-gray-800 rounded-lg border border-gray-700 flex items-center justify-center">
                                    <i class="fab fa-cc-mastercard text-2xl"></i>
                                </button>
                                <button class="p-3 bg-gray-800 rounded-lg border border-gray-700 flex items-center justify-center">
                                    <i class="fab fa-paypal text-2xl"></i>
                                </button>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Card Number</label>
                            <input type="text" placeholder="1234 5678 9012 3456" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium mb-1">Expiry Date</label>
                                <input type="text" placeholder="MM/YY" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">CVC</label>
                                <input type="text" placeholder="123" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                            </div>
                        </div>
                        <button onclick="processPayment(${amount})" class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-2 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all">
                            Pay ₱${amount}
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }
        
        function closePaymentModal() {
            const modal = document.getElementById('paymentModal');
            if (modal) modal.remove();
        }
        
        function processPayment(amount) {
            alert(`Payment of ₱${amount} processed successfully!`);
            closePaymentModal();
        }
        
        // Shopping cart functionality
        let cartItems = [];
        
        function toggleShoppingCart() {
            document.getElementById('shoppingCartPanel').classList.toggle('hidden');
        }
        
        function closeShoppingCart() {
            document.getElementById('shoppingCartPanel').classList.add('hidden');
        }
        
        function addToCart(product, price, image) {
            // Create a unique ID for the product
            const productId = Date.now();
            
            // Add to cart items array
            cartItems.push({
                id: productId,
                product: product,
                price: price,
                quantity: 1,
                image: image || 'https://via.placeholder.com/50'
            });
            
            // Update cart badge
            updateCartBadge();
            
            // Show notification
            showToast(`${product} added to cart!`);
        }
        
        function updateCartBadge() {
            const badge = document.getElementById('cartCountBadge');
            
            if (cartItems.length > 0) {
                badge.textContent = cartItems.length;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }
        }
        
        function showToast(message) {
            // Create and show toast notification
            const toast = document.createElement('div');
            toast.className = 'fixed bottom-4 right-4 bg-gradient-to-r from-pink-500 to-purple-600 text-white px-4 py-2 rounded-lg shadow-lg z-[200]';
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // Remove after 3 seconds
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
        
        function updateCartDisplay() {
            const cartItemsList = document.getElementById('cartItemsList');
            const cartEmptyMessage = document.getElementById('cartEmptyMessage');
            const cartItemsContainer = document.getElementById('cartItems');
            const subtotalElement = document.getElementById('cartSubtotal');
            const totalElement = document.getElementById('cartTotal');
            
            // Clear current items
            cartItemsList.innerHTML = '';
            
            if (cartItems.length === 0) {
                cartEmptyMessage.classList.remove('hidden');
                cartItemsContainer.classList.add('hidden');
                return;
            }
            
            // Show items container, hide empty message
            cartEmptyMessage.classList.add('hidden');
            cartItemsContainer.classList.remove('hidden');
            
            // Calculate subtotal
            let subtotal = 0;
            
            // Add each item to the cart display
            cartItems.forEach(item => {
                const itemTotal = item.price * item.quantity;
                subtotal += itemTotal;
                
                const itemElement = document.createElement('div');
                itemElement.className = 'flex items-center p-3 bg-gray-800 rounded-lg';
                itemElement.innerHTML = `
                    <img src="${item.image}" alt="${item.product}" class="w-12 h-12 rounded object-cover mr-3">
                    <div class="flex-1">
                        <div class="flex justify-between">
                            <span class="font-medium">${item.product}</span>
                            <span>₱${itemTotal.toLocaleString()}</span>
                        </div>
                        <div class="flex items-center mt-1">
                            <button class="p-1 bg-gray-700 rounded" onclick="decreaseQuantity(${item.id})">
                                <i class="fas fa-minus text-xs"></i>
                            </button>
                            <span class="mx-2">${item.quantity}</span>
                            <button class="p-1 bg-gray-700 rounded" onclick="increaseQuantity(${item.id})">
                                <i class="fas fa-plus text-xs"></i>
                            </button>
                            <button class="ml-auto p-1 text-gray-400 hover:text-red-500" onclick="removeFromCart(${item.id})">
                                <i class="fas fa-trash text-sm"></i>
                            </button>
                        </div>
                    </div>
                `;
                cartItemsList.appendChild(itemElement);
            });
            
            // Update totals
            subtotalElement.textContent = `₱${subtotal.toLocaleString()}`;
            totalElement.textContent = `₱${(subtotal + 150).toLocaleString()}`;
        }
        
        function increaseQuantity(itemId) {
            const item = cartItems.find(item => item.id === itemId);
            if (item) {
                item.quantity++;
                updateCartDisplay();
            }
        }
        
        function decreaseQuantity(itemId) {
            const item = cartItems.find(item => item.id === itemId);
            if (item && item.quantity > 1) {
                item.quantity--;
                updateCartDisplay();
            }
        }
        
        function removeFromCart(itemId) {
            cartItems = cartItems.filter(item => item.id !== itemId);
            updateCartBadge();
            updateCartDisplay();
        }
        
        function proceedToCheckout() {
            // Hide cart panel, show checkout panel
            document.getElementById('shoppingCartPanel').classList.add('hidden');
            document.getElementById('checkoutPanel').classList.remove('hidden');
            
            // Update checkout items summary
            updateCheckoutSummary();
        }
        
        function backToCart() {
            // Hide checkout panel, show cart panel
            document.getElementById('checkoutPanel').classList.add('hidden');
            document.getElementById('shoppingCartPanel').classList.remove('hidden');
        }
        
        function updateCheckoutSummary() {
            const checkoutItemsList = document.getElementById('checkoutItemsList');
            const subtotalElement = document.getElementById('checkoutSubtotal');
            const totalElement = document.getElementById('checkoutTotal');
            
            // Clear current items
            checkoutItemsList.innerHTML = '';
            
            // Calculate subtotal
            let subtotal = 0;
            
            // Add each item to the checkout summary
            cartItems.forEach(item => {
                const itemTotal = item.price * item.quantity;
                subtotal += itemTotal;
                
                const itemElement = document.createElement('div');
                itemElement.className = 'flex justify-between items-center';
                itemElement.innerHTML = `
                    <span class="text-sm">${item.product} x ${item.quantity}</span>
                    <span class="text-sm font-medium">₱${itemTotal.toLocaleString()}</span>
                `;
                checkoutItemsList.appendChild(itemElement);
            });
            
            // Update totals
            subtotalElement.textContent = `₱${subtotal.toLocaleString()}`;
            totalElement.textContent = `₱${(subtotal + 150).toLocaleString()}`;
        }
        
        function completeOrder() {
            alert('Thank you for your order! Your payment has been processed successfully.');
            
            // Clear cart
            cartItems = [];
            updateCartBadge();
            
            // Hide checkout panel
            document.getElementById('checkoutPanel').classList.add('hidden');
        }
        
        // Initialize cart when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            updateCartBadge();
            
            // Add click handlers to all "Add to Cart" buttons
            document.querySelectorAll('.add-to-cart-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const product = this.dataset.product;
                    const price = parseFloat(this.dataset.price);
                    const image = this.dataset.image;
                    addToCart(product, price, image);
                });
            });
        });

        // Bottom Slide Panel Functions
        function toggleBottomSlidePanel() {
            const panel = document.getElementById('bottomSlidePanel');
            const content = document.getElementById('bottomSlideContent');

            if (panel.classList.contains('hidden')) {
                // Show panel
                panel.classList.remove('hidden');
                setTimeout(() => {
                    content.classList.remove('translate-y-full');
                }, 10);
            } else {
                // Hide panel
                content.classList.add('translate-y-full');
                setTimeout(() => {
                    panel.classList.add('hidden');
                }, 300);
            }
        }

        function closeBottomSlidePanel(event) {
            // Only close if clicking on the backdrop, not the content
            if (event.target.id === 'bottomSlidePanel') {
                const content = document.getElementById('bottomSlideContent');
                content.classList.add('translate-y-full');
                setTimeout(() => {
                    document.getElementById('bottomSlidePanel').classList.add('hidden');
                }, 300);
            }
        }

        // Modal Functions for Bottom Panel Options
        function openOrdersModal() {
            closeBottomSlidePanel({ target: { id: 'bottomSlidePanel' } });
            showModal('ordersModal', `
                <h2 class="text-xl font-bold mb-4">My Orders</h2>
                <div class="space-y-4">
                    <div class="bg-gray-800 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold">Order #12345</span>
                            <span class="text-xs text-green-400">Delivered</span>
                        </div>
                        <p class="text-sm text-gray-400">Abstract Dreams Oil Painting</p>
                        <p class="text-sm font-medium">₱8,500.00</p>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold">Order #12344</span>
                            <span class="text-xs text-yellow-400">Processing</span>
                        </div>
                        <p class="text-sm text-gray-400">Digital Art Commission</p>
                        <p class="text-sm font-medium">₱3,500.00</p>
                    </div>
                </div>
            `);
        }

        function openVouchersModal() {
            closeBottomSlidePanel({ target: { id: 'bottomSlidePanel' } });
            showModal('vouchersModal', `
                <h2 class="text-xl font-bold mb-4">My Coupons</h2>
                <div class="space-y-3">
                    <div class="bg-gradient-to-r from-pink-600 to-purple-600 rounded-lg p-4">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="font-bold">20% OFF</h3>
                                <p class="text-xs opacity-90">Art supplies</p>
                            </div>
                            <button class="bg-white text-purple-600 px-3 py-1 rounded-full text-sm font-medium">
                                Use Now
                            </button>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-blue-600 to-teal-600 rounded-lg p-4">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="font-bold">₱500 OFF</h3>
                                <p class="text-xs opacity-90">Min spend ₱2000</p>
                            </div>
                            <button class="bg-white text-blue-600 px-3 py-1 rounded-full text-sm font-medium">
                                Use Now
                            </button>
                        </div>
                    </div>
                </div>
            `);
        }

        function openMessagesModal() {
            closeBottomSlidePanel({ target: { id: 'bottomSlidePanel' } });
            // Use the new TikTok-style messages page instead of modal
            showMessagesPage();
        }

        // Function to show the TikTok-style messages page
        window.showMessagesPage = function() {
            // Create full-screen overlay for messages page
            let messagesPage = document.getElementById('messagesPageOverlay');
            if (!messagesPage) {
                messagesPage = document.createElement('div');
                messagesPage.id = 'messagesPageOverlay';
                messagesPage.className = 'fixed inset-0 z-[300] bg-white';
                messagesPage.innerHTML = `
                    <!-- Header -->
                    <div class="sticky top-0 z-50 bg-white border-b border-gray-200">
                        <div class="flex items-center justify-center px-4 py-4 relative">
                            <button onclick="closeMessagesPage()" class="absolute left-4 p-2 rounded-full hover:bg-gray-100 transition-all">
                                <i class="fas fa-chevron-left text-xl text-black"></i>
                            </button>
                            <h1 class="text-xl font-bold text-black">Shop messages</h1>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="px-4 py-6 text-black bg-gray-50">
                        <!-- Shop Updates Section -->
                        <div class="mb-8">
                            <h2 class="text-lg font-medium mb-4 text-gray-500">Shop updates</h2>

                            <!-- Cancellation Request Update -->
                            <div class="message-item flex items-start space-x-4 p-4 mb-4 hover:bg-gray-100 transition-all bg-white rounded-lg">
                                <div class="shop-update-icon w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 bg-gray-100 border border-gray-200">
                                    <i class="fas fa-shopping-bag text-gray-600 text-lg"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h3 class="font-semibold text-black truncate">Your cancellation request was appro...</h3>
                                        <span class="text-gray-500 text-sm ml-2 flex-shrink-0">3/14</span>
                                    </div>
                                    <p class="text-gray-600 text-sm leading-relaxed">
                                        Your cancellation request for order 578212471084647611 was approved by TikTok.
                                    </p>
                                </div>
                            </div>

                            <!-- Order Placed Update -->
                            <div class="message-item flex items-start space-x-4 p-4 mb-4 hover:bg-gray-100 transition-all bg-white rounded-lg">
                                <div class="shop-update-icon w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 bg-gray-100 border border-gray-200">
                                    <i class="fas fa-shopping-bag text-gray-600 text-lg"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h3 class="font-semibold text-black">Order placed</h3>
                                        <span class="text-gray-500 text-sm ml-2 flex-shrink-0">3/14</span>
                                    </div>
                                    <p class="text-gray-600 text-sm leading-relaxed mb-3">
                                        Your order 578212471084647611 was submitted. Thanks for shopping with TikTok!
                                    </p>
                                    <!-- Order Image -->
                                    <div class="w-16 h-16 rounded-lg overflow-hidden border border-gray-200">
                                        <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&fit=crop"
                                             alt="Order item" class="w-full h-full object-cover">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Divider -->
                        <hr class="border-gray-200 my-6">

                        <!-- Messages Section -->
                        <div>
                            <h2 class="text-lg font-medium mb-4 text-gray-500">Messages</h2>

                            <!-- UNLIRICE RICE WHOLESALING -->
                            <div class="message-item flex items-center space-x-4 p-4 mb-4 cursor-pointer hover:bg-gray-100 transition-all bg-white rounded-lg" onclick="openChat('unlirice')">
                                <div class="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 border border-gray-200">
                                    <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=48&h=48&fit=crop"
                                         alt="UNLIRICE" class="w-full h-full object-cover">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between mb-1">
                                        <h3 class="font-semibold text-black truncate">UNLIRICE RICE WHOLESALING</h3>
                                        <span class="text-gray-500 text-sm flex-shrink-0">1d</span>
                                    </div>
                                    <p class="text-gray-600 text-sm truncate">Hello, How can we help you?</p>
                                </div>
                            </div>

                            <!-- kooruiphilippines -->
                            <div class="message-item flex items-center space-x-4 p-4 mb-4 cursor-pointer hover:bg-gray-100 transition-all bg-white rounded-lg" onclick="openChat('koorui')">
                                <div class="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 bg-red-500 flex items-center justify-center border border-gray-200">
                                    <span class="text-white font-bold text-lg">K</span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between mb-1">
                                        <h3 class="font-semibold text-black truncate">kooruiphilippines</h3>
                                        <span class="text-gray-500 text-sm flex-shrink-0">05-03</span>
                                    </div>
                                    <p class="text-gray-600 text-sm truncate">Good day, we've noticed that you can...</p>
                                </div>
                            </div>

                            <!-- DC STORE.PH -->
                            <div class="message-item flex items-center space-x-4 p-4 mb-4 cursor-pointer hover:bg-gray-100 transition-all bg-white rounded-lg" onclick="openChat('dcstore')">
                                <div class="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 border border-gray-200">
                                    <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=48&h=48&fit=crop"
                                         alt="DC STORE" class="w-full h-full object-cover">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between mb-1">
                                        <h3 class="font-semibold text-black truncate">DC STORE.PH</h3>
                                        <span class="text-gray-500 text-sm flex-shrink-0">02-19</span>
                                    </div>
                                    <p class="text-gray-600 text-sm truncate">Thank you for getting in touch. How ...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(messagesPage);
            }

            // Add touch feedback for mobile
            const messageItems = messagesPage.querySelectorAll('.message-item');
            messageItems.forEach(item => {
                item.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });

                item.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        }

        // Function to close messages page
        window.closeMessagesPage = function() {
            const messagesPage = document.getElementById('messagesPageOverlay');
            if (messagesPage) {
                messagesPage.remove();
            }
        };

        // Function to open individual chat
        window.openChat = function(chatId) {
            alert(`Opening chat with ${chatId} - Individual chat page to be implemented`);
        };

        function openBonusModal() {
            closeBottomSlidePanel({ target: { id: 'bottomSlidePanel' } });
            showModal('bonusModal', `
                <h2 class="text-xl font-bold mb-4">Bonus & Rewards</h2>
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-lg p-4 text-center">
                        <i class="fas fa-coins text-3xl mb-2"></i>
                        <h3 class="font-bold text-lg">1,250 Points</h3>
                        <p class="text-sm opacity-90">Available to redeem</p>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-4">
                        <h4 class="font-medium mb-2">Daily Check-in</h4>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-400">Day 3 of 7</span>
                            <button class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm">
                                Check In
                            </button>
                        </div>
                    </div>
                </div>
            `);
        }

        function openSellModal() {
            closeBottomSlidePanel({ target: { id: 'bottomSlidePanel' } });
            showModal('sellModal', `
                <h2 class="text-xl font-bold mb-4">Start Selling</h2>
                <div class="space-y-4">
                    <button class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-3 rounded-lg font-medium">
                        <i class="fas fa-plus mr-2"></i>Add New Product
                    </button>
                    <button class="w-full bg-gray-800 text-white py-3 rounded-lg font-medium">
                        <i class="fas fa-chart-line mr-2"></i>View Analytics
                    </button>
                    <button class="w-full bg-gray-800 text-white py-3 rounded-lg font-medium">
                        <i class="fas fa-cog mr-2"></i>Shop Settings
                    </button>
                </div>
            `);
        }

        function openAddressModal() {
            closeBottomSlidePanel({ target: { id: 'bottomSlidePanel' } });
            showModal('addressModal', `
                <h2 class="text-xl font-bold mb-4">My Addresses</h2>
                <div class="space-y-3">
                    <div class="bg-gray-800 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium">Home</span>
                            <span class="text-xs bg-purple-600 px-2 py-1 rounded-full">Default</span>
                        </div>
                        <p class="text-sm text-gray-400">123 Art Street, Creative City, 12345</p>
                    </div>
                    <button class="w-full bg-gray-800 text-white py-3 rounded-lg font-medium border-2 border-dashed border-gray-600">
                        <i class="fas fa-plus mr-2"></i>Add New Address
                    </button>
                </div>
            `);
        }

        function openPaymentModal() {
            closeBottomSlidePanel({ target: { id: 'bottomSlidePanel' } });
            showModal('paymentModal', `
                <h2 class="text-xl font-bold mb-4">Payment Methods</h2>
                <div class="space-y-3">
                    <div class="bg-gray-800 rounded-lg p-4 flex items-center space-x-3">
                        <i class="fab fa-cc-visa text-2xl text-blue-500"></i>
                        <div class="flex-1">
                            <p class="font-medium">**** **** **** 1234</p>
                            <p class="text-sm text-gray-400">Expires 12/25</p>
                        </div>
                        <span class="text-xs bg-purple-600 px-2 py-1 rounded-full">Default</span>
                    </div>
                    <button class="w-full bg-gray-800 text-white py-3 rounded-lg font-medium border-2 border-dashed border-gray-600">
                        <i class="fas fa-plus mr-2"></i>Add Payment Method
                    </button>
                </div>
            `);
        }

        function openHelpModal() {
            closeBottomSlidePanel({ target: { id: 'bottomSlidePanel' } });
            showModal('helpModal', `
                <h2 class="text-xl font-bold mb-4">Help & Support</h2>
                <div class="space-y-3">
                    <button class="w-full bg-gray-800 text-white py-3 rounded-lg font-medium text-left px-4">
                        <i class="fas fa-question-circle mr-3"></i>FAQ
                    </button>
                    <button class="w-full bg-gray-800 text-white py-3 rounded-lg font-medium text-left px-4">
                        <i class="fas fa-headset mr-3"></i>Contact Support
                    </button>
                    <button class="w-full bg-gray-800 text-white py-3 rounded-lg font-medium text-left px-4">
                        <i class="fas fa-file-alt mr-3"></i>Terms & Conditions
                    </button>
                    <button class="w-full bg-gray-800 text-white py-3 rounded-lg font-medium text-left px-4">
                        <i class="fas fa-shield-alt mr-3"></i>Privacy Policy
                    </button>
                </div>
            `);
        }

        // Shop Tab Switching Functions
        function switchToMarketplace() {
            // Close the bottom slider
            closeBottomSlidePanel({ target: { id: 'bottomSlidePanel' } });

            // Show marketplace content, hide my shop content
            const marketplaceContent = document.getElementById('marketplace-content');
            const myshopContent = document.getElementById('myshop-content');
            if (marketplaceContent) {
                marketplaceContent.classList.remove('hidden');
                marketplaceContent.style.display = 'block';
            }
            if (myshopContent) {
                myshopContent.classList.add('hidden');
                myshopContent.style.display = 'none';
            }
        }

        function switchToMyShop() {
            // Close the bottom slider
            closeBottomSlidePanel({ target: { id: 'bottomSlidePanel' } });

            // Show my shop content, hide marketplace content
            const marketplaceContent = document.getElementById('marketplace-content');
            const myshopContent = document.getElementById('myshop-content');
            if (marketplaceContent) {
                marketplaceContent.classList.add('hidden');
                marketplaceContent.style.display = 'none';
            }
            if (myshopContent) {
                myshopContent.classList.remove('hidden');
                myshopContent.style.display = 'block';
            }
        }

        // Generic modal function
        function showModal(id, content) {
            let modal = document.getElementById(id);
            if (!modal) {
                modal = document.createElement('div');
                modal.id = id;
                modal.className = 'fixed inset-0 bg-black/80 z-[200] flex items-center justify-center';
                modal.innerHTML = `
                    <div class="bg-gray-900 p-6 rounded-xl w-full max-w-md mx-4 relative">
                        <button onclick="document.getElementById('${id}').remove();" class="absolute top-4 right-4 p-1 rounded-full hover:bg-gray-700">
                            <i class="fas fa-times text-lg"></i>
                        </button>
                        ${content}
                    </div>
                `;
                document.body.appendChild(modal);
            }
        }

        // Initialize search functionality for the top bar
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('shop-search-input');
            if (searchInput) {
                searchInput.addEventListener('keyup', function(e) {
                    const term = e.target.value.toLowerCase();
                    const allProducts = document.querySelectorAll('.product-card');
                    allProducts.forEach(product => {
                        const productText = product.textContent.toLowerCase();
                        if (productText.includes(term)) {
                            product.style.display = 'block';
                        } else {
                            product.style.display = 'none';
                        }
                    });
                });
            }
        });

        // Filter by category
        function filterByCategory(category) {
            // Update active category
            document.querySelectorAll('.category-pill').forEach(pill => {
                if (pill.dataset.category === category) {
                    pill.classList.add('active');
                } else {
                    pill.classList.remove('active');
                }
            });
            
            // Implement filtering logic here
            console.log(`Filtering by category: ${category}`);
            
            // If category is busking, show tip buttons
            if (category === 'busking') {
                document.querySelectorAll('.tip-btn').forEach(btn => {
                    btn.style.display = 'block';
                });
            } else {
                document.querySelectorAll('.tip-btn').forEach(btn => {
                    btn.style.display = 'none';
                });
            }
        }
        
        // Show services subcategories
        function showServicesSubcategories() {
            const servicesBtn = document.querySelector('.services-btn');
            const servicesSubcategories = document.getElementById('servicesSubcategories');
            
            // Toggle visibility
            if (servicesSubcategories.classList.contains('hidden')) {
                servicesSubcategories.classList.remove('hidden');
            } else {
                servicesSubcategories.classList.add('hidden');
            }
        }
        
        function selectServiceCategory(category) {
            // Hide the subcategories panel
            document.getElementById('servicesSubcategories').classList.add('hidden');
            
            // Update selected category
            alert(`Selected service category: ${category}`);
            
            // If busking is selected, show tip buttons
            if (category === 'busking') {
                document.querySelectorAll('.tip-btn').forEach(btn => {
                    btn.style.display = 'block';
                });
            }
        }
        
        function openTipModal(button) {
            const artistName = button.dataset.artist;
            
            // Create and show tip modal
            const modal = document.createElement('div');
            modal.id = 'tipModal';
            modal.className = 'fixed inset-0 bg-black/90 z-[100] flex items-center justify-center';
            modal.innerHTML = `
                <div class="bg-gray-900 p-6 rounded-xl w-full max-w-md mx-4">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-bold">Send a Tip to ${artistName}</h2>
                        <button onclick="closeTipModal()" class="p-1 rounded-full hover:bg-gray-700">
                            <i class="fas fa-times text-lg"></i>
                        </button>
                    </div>
                    
                    <div class="mb-6 text-center">
                        <div class="text-sm mb-2">Select an amount or enter custom amount</div>
                        <div class="grid grid-cols-3 gap-3 mb-4">
                            <button class="p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-pink-500 tip-amount-btn" data-amount="50">₱50</button>
                            <button class="p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-pink-500 tip-amount-btn" data-amount="100">₱100</button>
                            <button class="p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-pink-500 tip-amount-btn" data-amount="200">₱200</button>
                            <button class="p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-pink-500 tip-amount-btn" data-amount="500">₱500</button>
                            <button class="p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-pink-500 tip-amount-btn" data-amount="1000">₱1,000</button>
                            <button class="p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-pink-500 flex items-center justify-center custom-tip-btn">
                                <i class="fas fa-plus mr-1"></i> Custom
                            </button>
                        </div>
                        <div id="customTipInput" class="hidden mb-4">
                            <input type="number" placeholder="Enter amount" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-1">Add a message (optional)</label>
                        <textarea class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500 h-16"></textarea>
                    </div>
                    
                    <button onclick="sendTip(100)" class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-2 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all">
                        Send Tip
                    </button>
                </div>
            `;
            document.body.appendChild(modal);
            
            // Add event listeners to tip amount buttons
            setTimeout(() => {
                document.querySelectorAll('.tip-amount-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        document.querySelectorAll('.tip-amount-btn').forEach(b => b.classList.remove('border-pink-500'));
                        this.classList.add('border-pink-500');
                        document.querySelector('#tipModal button:last-child').innerText = `Send Tip (₱${this.dataset.amount})`;
                    });
                });
                
                document.querySelector('.custom-tip-btn').addEventListener('click', function() {
                    document.getElementById('customTipInput').classList.toggle('hidden');
                });
            }, 100);
        }
        
        function closeTipModal() {
            const modal = document.getElementById('tipModal');
            if (modal) modal.remove();
        }
        
        function sendTip(amount) {
            alert(`Thank you for your tip of ₱${amount}!`);
            closeTipModal();
        }
        
        // Artist Portfolio Page
        function viewArtistPortfolio(username) {
            // Create and show artist portfolio modal
            const modal = document.createElement('div');
            modal.id = 'artistPortfolioModal';
            modal.className = 'fixed inset-0 bg-black/90 z-[100] flex items-center justify-center overflow-y-auto';
            modal.innerHTML = `
                <div class="bg-gray-900 w-full max-w-4xl max-h-[90vh] overflow-y-auto rounded-xl mx-4">
                    <!-- Artist Header -->
                    <div class="relative h-64 bg-gradient-to-r from-purple-900 to-pink-900">
                        <button onclick="closeArtistPortfolio()" class="absolute top-4 right-4 p-2 bg-black/50 rounded-full hover:bg-black/70">
                            <i class="fas fa-times text-lg"></i>
                        </button>
                        <div class="absolute -bottom-16 left-6">
                            <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop" 
                                alt="Artist" class="w-32 h-32 rounded-full border-4 border-gray-900">
                        </div>
                    </div>
                    
                    <!-- Artist Info -->
                    <div class="mt-20 px-6 pb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-2xl font-bold">@artista_maya</h2>
                                <p class="text-gray-400">Digital Artist | 3D Designer</p>
                            </div>
                            <div class="flex space-x-3">
                                <button class="px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full text-sm font-medium hover:from-pink-600 hover:to-purple-700 transition-all">
                                    Follow
                                </button>
                                <button class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium hover:bg-gray-700" onclick="openCommissionModal('artista_maya')">
                                    <i class="fas fa-palette mr-1"></i> Commission
                                </button>
                                <button class="px-4 py-2 bg-gray-800 rounded-full text-sm font-medium hover:bg-gray-700" onclick="openMessagesPanel()">
                                    <i class="fas fa-comment mr-1"></i> Message
                                </button>
                            </div>
                        </div>
                        
                        <!-- Artist Stats -->
                        <div class="flex space-x-6 mt-4">
                            <div class="text-center">
                                <div class="text-xl font-bold">248</div>
                                <div class="text-sm text-gray-400">Artworks</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold">15.7K</div>
                                <div class="text-sm text-gray-400">Followers</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold">1.2K</div>
                                <div class="text-sm text-gray-400">Following</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold">98%</div>
                                <div class="text-sm text-gray-400">Completion</div>
                            </div>
                        </div>
                        
                        <!-- Artist Bio -->
                        <div class="mt-6 p-4 bg-gray-800 rounded-lg">
                            <p>Digital artist specializing in concept art and character design. I love creating immersive worlds and bringing characters to life. Available for commissions and collaborations.</p>
                            <div class="flex items-center mt-2 text-sm text-gray-400">
                                <i class="fas fa-map-marker-alt mr-1"></i> Manila, Philippines
                                <span class="mx-2">•</span>
                                <i class="fas fa-calendar-alt mr-1"></i> Joined 2023
                            </div>
                        </div>
                        
                        <!-- Portfolio Categories -->
                        <div class="mt-6">
                            <div class="flex items-center space-x-4 mb-4 overflow-x-auto pb-2">
                                <button class="px-4 py-1 bg-gray-800 rounded-full text-sm font-medium active-category">All Works</button>
                                <button class="px-4 py-1 bg-gray-800 rounded-full text-sm font-medium">Digital Art</button>
                                <button class="px-4 py-1 bg-gray-800 rounded-full text-sm font-medium">Paintings</button>
                                <button class="px-4 py-1 bg-gray-800 rounded-full text-sm font-medium">Commissions</button>
                                <button class="px-4 py-1 bg-gray-800 rounded-full text-sm font-medium">For Sale</button>
                            </div>
                            
                            <!-- Portfolio Grid -->
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                                <div class="aspect-square bg-gradient-to-br from-purple-900 to-blue-900 rounded-lg overflow-hidden relative cursor-pointer">
                                    <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-black/70 transition-opacity">
                                        <div class="text-center">
                                            <div class="text-sm font-medium mb-1">Abstract Dreams</div>
                                            <div class="text-xs text-gray-400">₱8,500</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="aspect-square bg-gradient-to-br from-green-900 to-teal-900 rounded-lg overflow-hidden relative cursor-pointer">
                                    <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-black/70 transition-opacity">
                                        <div class="text-center">
                                            <div class="text-sm font-medium mb-1">Ocean Melody</div>
                                            <div class="text-xs text-gray-400">₱12,000</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="aspect-square bg-gradient-to-br from-red-900 to-orange-900 rounded-lg overflow-hidden relative cursor-pointer">
                                    <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-black/70 transition-opacity">
                                        <div class="text-center">
                                            <div class="text-sm font-medium mb-1">Fire Spirit</div>
                                            <div class="text-xs text-gray-400">₱9,200</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="aspect-square bg-gradient-to-br from-blue-900 to-cyan-900 rounded-lg overflow-hidden relative cursor-pointer">
                                    <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-black/70 transition-opacity">
                                        <div class="text-center">
                                            <div class="text-sm font-medium mb-1">Deep Blue</div>
                                            <div class="text-xs text-gray-400">₱7,500</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="aspect-square bg-gradient-to-br from-yellow-900 to-amber-900 rounded-lg overflow-hidden relative cursor-pointer">
                                    <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-black/70 transition-opacity">
                                        <div class="text-center">
                                            <div class="text-sm font-medium mb-1">Golden Hour</div>
                                            <div class="text-xs text-gray-400">₱11,000</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="aspect-square bg-gradient-to-br from-pink-900 to-rose-900 rounded-lg overflow-hidden relative cursor-pointer">
                                    <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-black/70 transition-opacity">
                                        <div class="text-center">
                                            <div class="text-sm font-medium mb-1">Rose Garden</div>
                                            <div class="text-xs text-gray-400">₱10,500</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Load More Button -->
                            <div class="text-center mt-6">
                                <button class="px-6 py-2 bg-gray-800 rounded-full text-sm font-medium hover:bg-gray-700">
                                    Load More
                                </button>
                            </div>
                        </div>
                        
                        <!-- Reviews Section -->
                        <div class="mt-8">
                            <h3 class="text-lg font-semibold mb-4">Reviews & Feedback</h3>
                            <div class="space-y-4">
                                <div class="bg-gray-800 rounded-lg p-4">
                                    <div class="flex items-start space-x-3">
                                        <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=30&h=30&fit=crop" 
                                             alt="User" class="w-10 h-10 rounded-full">
                                        <div>
                                            <div class="flex items-center">
                                                <span class="font-medium">@user1</span>
                                                <span class="mx-2 text-gray-500">•</span>
                                                <div class="flex">
                                                    <i class="fas fa-star text-yellow-500"></i>
                                                    <i class="fas fa-star text-yellow-500"></i>
                                                    <i class="fas fa-star text-yellow-500"></i>
                                                    <i class="fas fa-star text-yellow-500"></i>
                                                    <i class="fas fa-star text-yellow-500"></i>
                                                </div>
                                            </div>
                                            <p class="text-sm mt-1">Incredible artist! The commission was completed quickly and exceeded my expectations. Will definitely work with again!</p>
                                            <span class="text-xs text-gray-500 mt-1 block">2 months ago</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-gray-800 rounded-lg p-4">
                                    <div class="flex items-start space-x-3">
                                        <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=30&h=30&fit=crop" 
                                             alt="User" class="w-10 h-10 rounded-full">
                                        <div>
                                            <div class="flex items-center">
                                                <span class="font-medium">@user2</span>
                                                <span class="mx-2 text-gray-500">•</span>
                                                <div class="flex">
                                                    <i class="fas fa-star text-yellow-500"></i>
                                                    <i class="fas fa-star text-yellow-500"></i>
                                                    <i class="fas fa-star text-yellow-500"></i>
                                                    <i class="fas fa-star text-yellow-500"></i>
                                                    <i class="far fa-star text-yellow-500"></i>
                                                </div>
                                            </div>
                                            <p class="text-sm mt-1">Amazing work as always! Great communication throughout the process.</p>
                                            <span class="text-xs text-gray-500 mt-1 block">1 month ago</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }
        
        function closeArtistPortfolio() {
            const modal = document.getElementById('artistPortfolioModal');
            if (modal) modal.remove();
        }
        
        // Ensure all 'Request Service' buttons open the luthier request modal
        // Use event delegation for dynamically rendered or multiple buttons with the same class
        
        document.addEventListener('click', function(e) {
            if (e.target.closest('.buy-btn') && e.target.closest('.product-card[data-subcategory="guitar-luthier"]')) {
                openLuthierForm();
            }
        });
        
        // Add Product Modal - Show/hide Condition field based on selected category
        const productCategorySelect = document.getElementById('productCategory');
        if (productCategorySelect) {
            productCategorySelect.addEventListener('change', function () {
                const conditionContainer = document.getElementById('productConditionContainer');
                const conditionSelect = document.getElementById('productCondition');
                if (this.value === 'musical-instruments') {
                    conditionContainer.style.display = 'block';
                    conditionSelect.required = true;
                } else {
                    conditionContainer.style.display = 'none';
                    conditionSelect.required = false;
                    conditionSelect.value = ''; // Reset value when hidden
                }
            });
        }
        
        // Estimated Duration formatting logic
        const durationInput = document.getElementById('luthierDurationDays');
        const durationFormatted = document.getElementById('durationFormatted');
        
        if (durationInput) {
            durationInput.addEventListener('input', function() {
                const days = parseInt(durationInput.value, 10);
                if (!days || days < 1) {
                    durationFormatted.textContent = '';
                    return;
                }
                const weeks = Math.floor(days / 7);
                const remDays = days % 7;
                let formatted = '';
                if (weeks > 0) {
                    formatted += weeks + (weeks === 1 ? ' week' : ' weeks');
                }
                if (remDays > 0) {
                    if (weeks > 0) formatted += ' and ';
                    formatted += remDays + (remDays === 1 ? ' day' : ' days');
                }
                // Calculate end date
                const today = new Date();
                const endDate = new Date(today);
                endDate.setDate(today.getDate() + days);
                const options = { year: 'numeric', month: 'long', day: 'numeric' };
                formatted += ` (Ends on ${endDate.toLocaleDateString(undefined, options)})`;
                durationFormatted.textContent = formatted;
            });
        }
        
        
        
        // My Shop Content
        document.addEventListener('DOMContentLoaded', function() {
            // Show/hide Condition field based on Category selection in "Add Product" modal
            const productCategory = document.getElementById('productCategory');
            if (productCategory) {
                productCategory.addEventListener('change', function() {
                    const conditionContainer = document.getElementById('productConditionContainer');
                    if (this.value === 'musical-instruments') { // <-- Changed to plural
                        conditionContainer.style.display = 'block';
                    } else {
                        conditionContainer.style.display = 'none';
                    }
                });
            }
        });
        
        // Category Carousel Function
        function scrollCategories(direction) {
            const container = document.getElementById('categoriesContainer');
            const scrollAmount = 200; // Adjust this value to control scroll distance
            
            if (direction === 'left') {
                container.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            } else if (direction === 'right') {
                container.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }
        }
        
        // Show/hide instrument condition filter
        const instrumentCategoryBtn = document.querySelector('.category-btn[data-category="instruments"]');
        const instrumentConditionFilter = document.getElementById('instrumentConditionFilter');
        
        if (instrumentCategoryBtn && instrumentConditionFilter) {
            document.querySelectorAll('.category-btn').forEach(button => {
                button.addEventListener('click', () => {
                    if (button.dataset.category === 'instruments') {
                        instrumentConditionFilter.style.display = 'block';
                    } else if (button.dataset.category !== 'services') {
                        instrumentConditionFilter.style.display = 'none';
                    }
                });
            });
        }
        
        function toggleInstrumentConditionDropdown() {
            const dropdown = document.getElementById('instrumentConditionDropdown');
            dropdown.classList.toggle('hidden');
        }
        
        // Instrument condition subcategory filtering
        document.querySelectorAll('.instrument-condition-subcategory').forEach(button => {
            button.addEventListener('click', function() {
                const subcategory = this.dataset.subcategory;
                const products = document.querySelectorAll('.product-card');
                
                products.forEach(product => {
                    if (product.dataset.category === 'instruments' && (product.dataset.condition === subcategory || subcategory === 'all')) {
                        product.style.display = 'block';
                    } else if (product.dataset.category !== 'instruments') {
                        // Keep non-instrument items hidden
                    }
                    else {
                        product.style.display = 'none';
                    }
                });
                
                document.getElementById('instrumentConditionDropdown').classList.add('hidden');
            });
        });