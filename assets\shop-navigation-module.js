document.addEventListener("DOMContentLoaded", function () {
  // 1. Top Navigation Button Injection (with modals/panels for each)
  const shopNav = document.querySelector("#shop-content .flex.justify-center.space-x-4");
  if (shopNav) {
    // Remove old nav if any, then inject new nav
    shopNav.innerHTML = `
      <button id="shop-voucher-btn" class="top-nav-item px-4 py-2 rounded-full text-sm font-medium hover:scale-105 transition-all"><i class="fas fa-ticket-alt mr-2"></i>Voucher</button>
      <button id="shop-orders-btn" class="top-nav-item px-4 py-2 rounded-full text-sm font-medium hover:scale-105 transition-all"><i class="fas fa-box mr-2"></i>Orders</button>
      <button id="shop-payment-btn" class="top-nav-item px-4 py-2 rounded-full text-sm font-medium hover:scale-105 transition-all"><i class="fas fa-credit-card mr-2"></i>Payment</button>
      <button id="shop-messages-btn" class="top-nav-item px-4 py-2 rounded-full text-sm font-medium hover:scale-105 transition-all"><i class="fas fa-envelope mr-2"></i>Messages</button>
      <button id="shop-returns-btn" class="top-nav-item px-4 py-2 rounded-full text-sm font-medium hover:scale-105 transition-all"><i class="fas fa-undo mr-2"></i>Returns</button>
      <button id="shop-address-btn" class="top-nav-item px-4 py-2 rounded-full text-sm font-medium hover:scale-105 transition-all"><i class="fas fa-map-marked-alt mr-2"></i>Address</button>
    `;
  }

  // 2. Add event listeners for each button to open modals/panels
  function showShopModal(id, contentHTML, onOpen) {
    let modal = document.getElementById(id);
    if (!modal) {
      modal = document.createElement("div");
      modal.id = id;
      modal.className = "fixed inset-0 bg-black/80 z-[200] flex items-center justify-center";
      modal.innerHTML = `
        <div class="bg-gray-900 p-6 rounded-xl w-full max-w-md mx-4 relative">
          <button onclick="document.getElementById('${id}').remove();" class="absolute top-4 right-4 p-1 rounded-full hover:bg-gray-700">
            <i class="fas fa-times text-lg"></i>
          </button>
          ${contentHTML}
        </div>
      `;
      document.body.appendChild(modal);
    }
    if (onOpen) onOpen(modal);
  }

  // Voucher Button
  document.getElementById("shop-voucher-btn").onclick = function () {
    showShopModal(
      "shopVoucherModal",
      `
      <h2 class="text-xl font-bold mb-4">My Vouchers</h2>
      <div id="voucher-list" class="mb-4"></div>
      <form id="voucher-redeem-form" class="flex gap-2">
        <input type="text" id="voucher-code-input" placeholder="Enter voucher code..." class="flex-1 bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-sm" />
        <button type="submit" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition">Redeem</button>
      </form>
      <div id="voucher-message" class="mt-2 text-sm"></div>
      `,
      function (modal) {
        // Fetch and render available vouchers (API)
        fetch("/api/vouchers")
          .then(resp => resp.json())
          .then(data => {
            const list = modal.querySelector("#voucher-list");
            if (data.length === 0) {
              list.innerHTML = `<div class="text-gray-400">No vouchers available.</div>`;
            } else {
              list.innerHTML = data.map(v =>
                `<div class="bg-gray-800 rounded-lg px-4 py-2 mb-2 flex justify-between items-center">
                  <div>
                    <span class="font-semibold">${v.code}</span>
                    <span class="ml-2 text-xs text-gray-400">${v.description}</span>
                    <span class="ml-2 text-xs text-green-400">${v.discount}% OFF</span>
                  </div>
                  <button onclick="navigator.clipboard.writeText('${v.code}')" class="text-xs bg-gray-700 rounded px-2 py-1">Copy</button>
                </div>`
              ).join("");
            }
          });
        // Voucher redeem form submit
        modal.querySelector("#voucher-redeem-form").onsubmit = function (e) {
          e.preventDefault();
          const code = modal.querySelector("#voucher-code-input").value.trim();
          fetch("/api/vouchers/redeem", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ code })
          }).then(r => r.json())
            .then(resp => {
              modal.querySelector("#voucher-message").textContent = resp.message || (resp.success ? "Voucher redeemed!" : "Invalid voucher.");
              if (resp.success) setTimeout(() => modal.remove(), 1500);
            })
            .catch(() => { modal.querySelector("#voucher-message").textContent = "Error redeeming voucher."; });
        };
      }
    );
  };

  // Orders Button
  document.getElementById("shop-orders-btn").onclick = function () {
    showShopModal(
      "shopOrdersModal",
      `
      <h2 class="text-xl font-bold mb-4">My Orders</h2>
      <div id="order-list" class="space-y-4"></div>
      `,
      function (modal) {
        fetch("/api/orders")
          .then(resp => resp.json())
          .then(data => {
            const list = modal.querySelector("#order-list");
            if (!data.length) {
              list.innerHTML = `<div class="text-gray-400">No orders found.</div>`;
            } else {
              list.innerHTML = data.map(order => `
                <div class="bg-gray-800 rounded-lg p-4">
                  <div class="flex justify-between items-center mb-2">
                    <span class="font-semibold">Order #${order.id}</span>
                    <span class="text-xs ${order.status === 'Delivered' ? 'text-green-400' : 'text-yellow-400'}">${order.status}</span>
                  </div>
                  <div class="text-xs text-gray-400 mb-1">${order.date}</div>
                  <div class="text-sm mb-2">${order.items.map(i => `${i.title} x${i.qty}`).join(", ")}</div>
                  <div class="font-bold text-purple-400">₱${order.total.toLocaleString()}</div>
                  <button onclick="fetch('/api/orders/${order.id}/details').then(r=>r.json()).then(d=>alert(JSON.stringify(d,null,2)))" class="mt-2 text-xs px-3 py-1 bg-gray-700 rounded">View Details</button>
                </div>
              `).join("");
            }
          });
      }
    );
  };

  // Payment Button
  document.getElementById("shop-payment-btn").onclick = function () {
    showShopModal(
      "shopPaymentModal",
      `
      <h2 class="text-xl font-bold mb-4">Payment Methods</h2>
      <div id="payment-methods-list"></div>
      <button id="add-payment-method-btn" class="mt-4 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition">Add Method</button>
      `,
      function (modal) {
        function loadMethods() {
          fetch("/api/payments/methods")
            .then(resp => resp.json())
            .then(data => {
              const list = modal.querySelector("#payment-methods-list");
              if (!data.length) {
                list.innerHTML = `<div class="text-gray-400">No payment methods saved.</div>`;
              } else {
                list.innerHTML = data.map(pm => `
                  <div class="bg-gray-800 rounded-lg px-4 py-2 mb-2 flex justify-between items-center">
                    <span>${pm.type === 'paypal' ? '<i class="fab fa-paypal text-blue-400"></i>' : pm.type === 'gcash' ? '<img src="https://upload.wikimedia.org/wikipedia/commons/0/09/GCash_logo.png" alt="GCash" style="height:20px;display:inline;margin-right:4px;">' : '<i class="fas fa-credit-card"></i>'}
                    ${pm.details}</span>
                    <button onclick="fetch('/api/payments/methods/${pm.id}',{method:'DELETE'}).then(()=>loadMethods())" class="text-xs bg-gray-700 rounded px-2 py-1">Remove</button>
                  </div>
                `).join("");
              }
            });
        }
        loadMethods();
        modal.querySelector("#add-payment-method-btn").onclick = function () {
          // Example: show add method form (could be modal, stubbed for now)
          alert("Show Add Payment Method Form (GCash, PayPal, Card, etc).");
        };
      }
    );
  };

  // Messages Button - Navigate to dedicated messages page
  document.getElementById("shop-messages-btn").onclick = function () {
    // Create and show the messages page as a full-screen overlay
    showMessagesPage();
  };

  // Returns Button
  document.getElementById("shop-returns-btn").onclick = function () {
    showShopModal(
      "shopReturnsModal",
      `
      <h2 class="text-xl font-bold mb-4">Return Requests</h2>
      <div id="returns-list" class="mb-4"></div>
      <button id="create-return-btn" class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition">Request Return</button>
      `,
      function (modal) {
        function loadReturns() {
          fetch("/api/returns")
            .then(r => r.json())
            .then(data => {
              const list = modal.querySelector("#returns-list");
              if (!data.length) {
                list.innerHTML = `<div class="text-gray-400">No return requests.</div>`;
              } else {
                list.innerHTML = data.map(ret =>
                  `<div class="bg-gray-800 rounded-lg p-3 mb-2">
                    <div class="flex justify-between items-center mb-1">
                      <span>Order #${ret.order_id}</span>
                      <span class="text-xs text-yellow-400">${ret.status}</span>
                    </div>
                    <div class="text-xs text-gray-400">Reason: ${ret.reason}</div>
                  </div>`
                ).join("");
              }
            });
        }
        loadReturns();
        modal.querySelector("#create-return-btn").onclick = function () {
          // Show a simple prompt for demo
          const orderId = prompt("Enter Order ID for return:");
          const reason = prompt("Reason for return:");
          if (!orderId || !reason) return;
          fetch("/api/returns", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ order_id: orderId, reason })
          }).then(() => loadReturns());
        };
      }
    );
  };

  // Address Button
  document.getElementById("shop-address-btn").onclick = function () {
    showShopModal(
      "shopAddressModal",
      `
      <h2 class="text-xl font-bold mb-4">Manage Addresses</h2>
      <div id="address-list"></div>
      <button id="add-address-btn" class="mt-4 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition">Add Address</button>
      `,
      function (modal) {
        function loadAddresses() {
          fetch("/api/addresses")
            .then(r => r.json())
            .then(data => {
              const list = modal.querySelector("#address-list");
              if (!data.length) {
                list.innerHTML = `<div class="text-gray-400">No addresses found.</div>`;
              } else {
                list.innerHTML = data.map(addr =>
                  `<div class="bg-gray-800 rounded-lg px-4 py-2 mb-2 flex justify-between items-center">
                    <div>
                      <div class="font-semibold">${addr.label || addr.name}</div>
                      <div class="text-xs text-gray-400">${addr.address}, ${addr.city}, ${addr.postal} <span class="ml-1">${addr.type ? '(' + addr.type + ')' : ''}</span></div>
                    </div>
                    <button onclick="fetch('/api/addresses/${addr.id}',{method:'DELETE'}).then(()=>loadAddresses())" class="text-xs bg-gray-700 rounded px-2 py-1">Delete</button>
                  </div>`
                ).join("");
              }
            });
        }
        loadAddresses();
        modal.querySelector("#add-address-btn").onclick = function () {
          // Show a simple prompt for demo
          const name = prompt("Name/Label:");
          const address = prompt("Address:");
          const city = prompt("City:");
          const postal = prompt("Postal Code:");
          if (!name || !address || !city || !postal) return;
          fetch("/api/addresses", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ name, address, city, postal })
          }).then(() => loadAddresses());
        };
      }
    );
  };

  // ========== CATEGORIES & SUBCATEGORIES ==========
  // Move search bar below categories and make categories dynamic
  const mktContent = document.getElementById('marketplace-content');
  if (mktContent) {
    // Remove old categories and search bar
    const oldCat = mktContent.querySelector('.px-4.mb-6');
    if (oldCat) oldCat.remove();

    // Insert the new dynamic category panel at the top of marketplace-content
    const catDiv = document.createElement('div');
    catDiv.className = 'px-4 mb-4';
    catDiv.innerHTML = `
      <!-- Dynamic Categories/Subcategories (fetched via API) -->
      <div id="dynamic-categories" class="mb-2"></div>
      <!-- Search Bar (below categories) -->
      <div class="relative mb-6">
        <input type="text" id="shop-search-input" placeholder="Search products, artists..." class="w-full bg-gray-800 border border-gray-700 rounded-full px-4 py-3 pl-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500" />
        <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
      </div>
    `;
    mktContent.prepend(catDiv);

    // Fetch categories & subcategories from backend
    fetch("/api/categories")
      .then(resp => resp.json())
      .then(categories => {
        // Example fallback for demo:
        if (!Array.isArray(categories) || !categories.length) {
          categories = [
            { name: "All", subcategories: [] },
            { name: "Paintings", subcategories: ["Artwork", "Prints"] },
            { name: "Drawings", subcategories: ["Charcoal", "Pencil", "Sketch"] },
            { name: "Sculptures", subcategories: ["Wood", "Clay", "Wire", "Steel"] },
            { name: "Art Materials", subcategories: ["Acrylic paints", "Oil paints", "Watercolor paints", "Gouache paints", "Spray paints / Airbrush paints", "Paint palettes", "Palette knives", "Mediums & varnishes", "Paint thinners", "Solvents", "Gesso", "Graphite pencils", "Colored pencils", "Charcoal sticks & pencils", "Pastels", "Conte crayons", "Erasers", "Sharpeners & Blades", "Blending stumps", "Tortillons", "Inks", "Technical pens", "Clay", "Stone blocks", "Wood carving blocks", "Plaster", "Resin kits", "Armatures", "Wire frames", "Molds & Mold-making materials", "Safety gear", "Canvases", "Canvas boards", "Panels", "Paper pads", "Bristol boards", "Wood panels", "Fabric for painting", "Glass/acrylic sheets for reverse painting", "Frames", "Frame mats", "Borders", "Hanging kits & wires", "Fixatives", "Sealants", "Varnish sprays", "Mounting adhesives", "Art storage & organization: Art bins", "Brush holders", "Easels", "Art desks", "Portfolios", "Art folders", "Travel kits", "Lighting equipment for art", "Drawing tablets/styluses", "Graphic tablets with screens", "Brushes", "Markers", "Stencil sets", "Tape", "Rulers", "Compasses", "Grids"] },
            { name: "Art Services", subcategories: ["Custom portrait", "Painting commissions", "Airbrush artwork (canvas, clothing, vehicles)", "Tattoo designs", "Calligraphy & lettering", "Digital illustration (logos, posters)", "Character design", "NFT creation & minting", "Mural painting", "Art restoration", "Photography & editing", "Video editing & animation", "Curation & gallery setup"] },
            { name: "Music Services", subcategories: ["Live performance bookings (bands, DJs, solo artists)", "Session musicians", "Voice coaching & vocal tracking", "Instrument lessons", "Beat production", "Mixing & mastering", "Songwriting & lyric writing", "Jingle creation", "DJ & event sound services", "Studio & equipment rental"] },
            { name: "Musical Instruments", subcategories: ["Brand new", "Secondhand"] },
            { name: "Electronics", subcategories: ["USB Audio Interfaces", "Thunderbolt Interfaces", "Portable/Mobile Interfaces", "Multi-channel Mixers with Interface", "Microphones (Condenser, Dynamic, Lavalier, USB, Broadcast)", "Microphone stands & shock mounts", "Pop filters & windshields", "Headphones & Monitors (Studio headphones, IEMs, DJ headphones, Studio monitors, Isolation pads)", "Digital Instruments (MIDI controllers, Digital pianos, Synthesizers, Drum machines, Samplers, Electronic drum pads, Keytar controllers)", "DJ & Live Gear (Controllers, Mixers, Turntables, CDJs, Effects units, Lighting controllers, Audio interfaces)", "Production Hardware (Field recorders, Preamps, Patchbays, Channel strips, EQs & compressors, Control surfaces)", "Software & Licenses (DAWs, VST plugins, Loop/sample libraries, Audio editors, Synth software, Virtual amps & effects)", "Accessories & Cables (XLR, TRS, RCA, MIDI cables, DI boxes, Organizers, Adapters, Racks, Soundproofing, USB hubs/stands)"] }
          ];
        }
        const catDiv = document.getElementById("dynamic-categories");
        catDiv.innerHTML = categories.map((cat, idx) => `
          <div class="inline-block mr-2 mb-2">
            <button class="category-btn bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap flex items-center ${idx === 0 ? "bg-purple-600 text-white" : ""}" data-category="${cat.name}" type="button">
              ${cat.name}
              ${cat.subcategories && cat.subcategories.length ? '<i class="fas fa-chevron-down ml-2 text-xs"></i>' : ''}
            </button>
            ${cat.subcategories && cat.subcategories.length ? `
              <div class="subcategory-dropdown hidden absolute z-20 bg-gray-900 rounded-lg shadow-lg mt-2 p-2 min-w-48 max-h-60 overflow-y-auto border border-gray-700" style="min-width:200px;">
                ${cat.subcategories.map(sub => `
                  <button class="subcategory-btn w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded" data-category="${cat.name}" data-subcategory="${sub}">${sub}</button>
                `).join("")}
              </div>
            ` : ""}
          </div>
        `).join("");

        // Add dropdown toggle logic for subcategories
        catDiv.querySelectorAll('.category-btn').forEach((btn, i) => {
          btn.addEventListener('click', function (e) {
            // Remove highlight from all
            catDiv.querySelectorAll('.category-btn').forEach(x => x.classList.remove('bg-purple-600', 'text-white'));
            btn.classList.add('bg-purple-600', 'text-white');
            // Hide all dropdowns
            catDiv.querySelectorAll('.subcategory-dropdown').forEach(x => x.classList.add('hidden'));
            // Show dropdown if any
            const dd = btn.parentElement.querySelector('.subcategory-dropdown');
            if (dd) dd.classList.toggle('hidden');
            // Filter products by category
            filterProductsByCategory(btn.dataset.category);
          });
        });
        // Subcategory click
        catDiv.querySelectorAll('.subcategory-btn').forEach(subBtn => {
          subBtn.addEventListener('click', function (e) {
            e.stopPropagation();
            filterProductsBySubcategory(subBtn.dataset.category, subBtn.dataset.subcategory);
            // Hide all dropdowns after
            catDiv.querySelectorAll('.subcategory-dropdown').forEach(x => x.classList.add('hidden'));
          });
        });
        // Hide dropdowns when clicking outside
        document.addEventListener('click', function (e) {
          if (!catDiv.contains(e.target)) {
            catDiv.querySelectorAll('.subcategory-dropdown').forEach(x => x.classList.add('hidden'));
          }
        });

        // Filtering logic
        function filterProductsByCategory(category) {
          const allProducts = mktContent.querySelectorAll('.product-card');
          if (category === 'All') {
            allProducts.forEach(p => p.style.display = 'block');
          } else {
            allProducts.forEach(p => {
              p.style.display = (p.getAttribute('data-category') && p.getAttribute('data-category').toLowerCase().includes(category.toLowerCase())) ? 'block' : 'none';
            });
          }
        }
        function filterProductsBySubcategory(category, subcategory) {
          const allProducts = mktContent.querySelectorAll('.product-card');
          // Here you can use a custom data-subcategory tag for each product, or just match text
          allProducts.forEach(p => {
            const prodCat = (p.getAttribute('data-category') || "").toLowerCase();
            const prodSub = (p.getAttribute('data-subcategory') || "").toLowerCase();
            if (prodCat.includes(category.toLowerCase()) && prodSub.includes(subcategory.toLowerCase())) {
              p.style.display = 'block';
            } else if (p.textContent.toLowerCase().includes(subcategory.toLowerCase())) {
              p.style.display = 'block';
            } else {
              p.style.display = 'none';
            }
          });
        }
      });

    // Search bar logic
    catDiv.querySelector("#shop-search-input").addEventListener("keyup", function (e) {
      const term = e.target.value.toLowerCase();
      const allProducts = mktContent.querySelectorAll('.product-card');
      allProducts.forEach(p => {
        p.style.display = p.textContent.toLowerCase().includes(term) ? 'block' : 'none';
      });
    });
  }

  // Function to show the TikTok-style messages page
  function showMessagesPage() {
    // Create full-screen overlay for messages page
    let messagesPage = document.getElementById('messagesPageOverlay');
    if (!messagesPage) {
      messagesPage = document.createElement('div');
      messagesPage.id = 'messagesPageOverlay';
      messagesPage.className = 'fixed inset-0 z-[300] bg-white';
      messagesPage.innerHTML = `
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-white border-b border-gray-200">
            <div class="flex items-center justify-center px-4 py-4 relative">
                <button onclick="closeMessagesPage()" class="absolute left-4 p-2 rounded-full hover:bg-gray-100 transition-all">
                    <i class="fas fa-chevron-left text-xl text-black"></i>
                </button>
                <h1 class="text-xl font-bold text-black">Shop messages</h1>
            </div>
        </div>

        <!-- Content -->
        <div class="px-4 py-6 text-black bg-gray-50">
            <!-- Shop Updates Section -->
            <div class="mb-8">
                <h2 class="text-lg font-medium mb-4 text-gray-500">Shop updates</h2>

                <!-- Cancellation Request Update -->
                <div class="message-item flex items-start space-x-4 p-4 mb-4 hover:bg-gray-100 transition-all bg-white rounded-lg">
                    <div class="shop-update-icon w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 bg-gray-100 border border-gray-200">
                        <i class="fas fa-shopping-bag text-gray-600 text-lg"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between mb-1">
                            <h3 class="font-semibold text-black truncate">Your cancellation request was appro...</h3>
                            <span class="text-gray-500 text-sm ml-2 flex-shrink-0">3/14</span>
                        </div>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Your cancellation request for order 578212471084647611 was approved by TikTok.
                        </p>
                    </div>
                </div>

                <!-- Order Placed Update -->
                <div class="message-item flex items-start space-x-4 p-4 mb-4 hover:bg-gray-100 transition-all bg-white rounded-lg">
                    <div class="shop-update-icon w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 bg-gray-100 border border-gray-200">
                        <i class="fas fa-shopping-bag text-gray-600 text-lg"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between mb-1">
                            <h3 class="font-semibold text-black">Order placed</h3>
                            <span class="text-gray-500 text-sm ml-2 flex-shrink-0">3/14</span>
                        </div>
                        <p class="text-gray-600 text-sm leading-relaxed mb-3">
                            Your order 578212471084647611 was submitted. Thanks for shopping with TikTok!
                        </p>
                        <!-- Order Image -->
                        <div class="w-16 h-16 rounded-lg overflow-hidden border border-gray-200">
                            <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&fit=crop"
                                 alt="Order item" class="w-full h-full object-cover">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Divider -->
            <hr class="border-gray-200 my-6">

            <!-- Messages Section -->
            <div>
                <h2 class="text-lg font-medium mb-4 text-gray-500">Messages</h2>

                <!-- UNLIRICE RICE WHOLESALING -->
                <div class="message-item flex items-center space-x-4 p-4 mb-4 cursor-pointer hover:bg-gray-100 transition-all bg-white rounded-lg" onclick="openChat('unlirice')">
                    <div class="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 border border-gray-200">
                        <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=48&h=48&fit=crop"
                             alt="UNLIRICE" class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-black truncate">UNLIRICE RICE WHOLESALING</h3>
                            <span class="text-gray-500 text-sm flex-shrink-0">1d</span>
                        </div>
                        <p class="text-gray-600 text-sm truncate">Hello, How can we help you?</p>
                    </div>
                </div>

                <!-- kooruiphilippines -->
                <div class="message-item flex items-center space-x-4 p-4 mb-4 cursor-pointer hover:bg-gray-100 transition-all bg-white rounded-lg" onclick="openChat('koorui')">
                    <div class="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 bg-red-500 flex items-center justify-center border border-gray-200">
                        <span class="text-white font-bold text-lg">K</span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-black truncate">kooruiphilippines</h3>
                            <span class="text-gray-500 text-sm flex-shrink-0">05-03</span>
                        </div>
                        <p class="text-gray-600 text-sm truncate">Good day, we've noticed that you can...</p>
                    </div>
                </div>

                <!-- DC STORE.PH -->
                <div class="message-item flex items-center space-x-4 p-4 mb-4 cursor-pointer hover:bg-gray-100 transition-all bg-white rounded-lg" onclick="openChat('dcstore')">
                    <div class="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 border border-gray-200">
                        <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=48&h=48&fit=crop"
                             alt="DC STORE" class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-black truncate">DC STORE.PH</h3>
                            <span class="text-gray-500 text-sm flex-shrink-0">02-19</span>
                        </div>
                        <p class="text-gray-600 text-sm truncate">Thank you for getting in touch. How ...</p>
                    </div>
                </div>
            </div>
        </div>
      `;
      document.body.appendChild(messagesPage);
    }

    // Add touch feedback for mobile
    const messageItems = messagesPage.querySelectorAll('.message-item');
    messageItems.forEach(item => {
      item.addEventListener('touchstart', function() {
        this.style.transform = 'scale(0.98)';
      });

      item.addEventListener('touchend', function() {
        this.style.transform = 'scale(1)';
      });
    });
  }

  // Function to close messages page
  window.closeMessagesPage = function() {
    const messagesPage = document.getElementById('messagesPageOverlay');
    if (messagesPage) {
      messagesPage.remove();
    }
  };

  // Function to open individual chat
  window.openChat = function(chatId) {
    alert(`Opening chat with ${chatId} - Individual chat page to be implemented`);
  };

});